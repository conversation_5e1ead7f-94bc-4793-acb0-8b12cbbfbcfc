import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class CompanyService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Get all companies
   * @returns Observable with companies list
   * @originalName getAllEntreprise
   */
  getAllCompanies(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'entreprises/', httpOptions);
  }
}
