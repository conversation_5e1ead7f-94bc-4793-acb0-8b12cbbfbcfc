import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class InvoiceService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Get invoice by ID
   * @param id - Invoice ID
   * @returns Observable with invoice data
   */
  getInvoice(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'facture/' + id, httpOptions);
  }

  /**
   * Get invoice by expeditor
   * @param id - Expeditor ID
   * @returns Observable with invoice data
   */
  getInvoiceByExpeditor(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'facturebyexpediteur/' + id, httpOptions);
  }

  /**
   * Get all invoices with pagination and filters
   * @param page - Page number
   * @param pageSize - Page size
   * @param clientId - Optional client ID filter
   * @param invoiceDate - Optional invoice date filter
   * @returns Observable with invoices list
   */
  getAllInvoices(page: number, pageSize: number, clientId?: any, invoiceDate?: string): Observable<any> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());

    if (clientId) {
      params = params.set('id_client', clientId.toString());
    }

    if (invoiceDate) {
      params = params.set('invoice_date', invoiceDate);
    }

    return this.http.get<any>(`${this.apiURL}allfacture/`, { ...httpOptions, params });
  }

  /**
   * Generate/Create invoice
   * @param invoiceData - Invoice data
   * @returns Observable with creation result
   */
  generateInvoice(invoiceData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'facture', invoiceData, httpOptions);
  }

  /**
   * Add new invoice
   * @param invoiceData - Invoice data
   * @returns Observable with creation result
   */
  addInvoice(invoiceData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'facture', invoiceData, httpOptions);
  }

  /**
   * Search invoice lines
   * @param searchData - Search criteria
   * @returns Observable with search results
   */
  searchInvoiceLines(searchData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'facture/search', searchData, httpOptions);
  }

  /**
   * Convert number to text
   * @param num - Number to convert
   * @returns Observable with converted text
   */
  convertNumberToText(num: any): Observable<any> {
    return this.http.post<any>(this.apiURL + `convert/${num}`, null, httpOptions);
  }

  /**
   * Send invoice email
   * @param base64PNG - Base64 encoded PNG image
   * @returns Observable with email sending result
   */
  sendInvoiceEmail(base64PNG: string): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('base64PNG', base64PNG);
    return this.http.post<any>(this.apiURL + 'facture/sendFacture', formData, httpOptions);
  }

  /**
   * Send invoice
   * @param data - Invoice data
   * @returns Promise with sending result
   */
  sendInvoice(data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(this.apiURL + 'facture/sendFacture', data, httpOptions)
        .toPromise()
        .then((response) => {
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * Create invoice for flux
   * @param invoiceData - Invoice data
   * @returns Observable with creation result
   */
  createInvoiceFlux(invoiceData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'createFactureFlux', invoiceData, httpOptions);
  }

  /**
   * Create invoice for storage
   * @param invoiceData - Invoice data
   * @returns Observable with creation result
   */
  createInvoiceStorage(invoiceData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'createFactureEntreposage', invoiceData, httpOptions);
  }

  /**
   * Send invoice to Sage
   * @param invoiceData - Invoice data
   * @returns Observable with sending result
   */
  sendToSage(invoiceData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'sendToSage', invoiceData, httpOptions);
  }

  /**
   * Find invoice by date
   * @param dateData - Date criteria
   * @returns Observable with invoices list
   */
  findInvoiceByDate(dateData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'findInvoiceByDate', dateData, httpOptions);
  }

  /**
   * Find invoices by client and date
   * @param clientId - Client ID
   * @param invoiceDate - Invoice date (format: MMYYYY)
   * @param page - Page number
   * @param pageSize - Page size
   * @returns Observable with invoices list
   */
  findInvoicesByClientAndDate(clientId: any, invoiceDate: string, page: number, pageSize: number): Observable<any> {
    const params = new HttpParams()
      .set('id_client', clientId.toString())
      .set('invoiced_date', invoiceDate)
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());

    return this.http.get<any>(`${this.apiURL}findFactureByClientAndDate`, { ...httpOptions, params });
  }
}
