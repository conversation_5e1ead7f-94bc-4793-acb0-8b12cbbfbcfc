// Here you can add other styles

// custom .chartjs-tooltip-body-item padding
@use "charts";

// custom tweaks for scrollbar styling (wip)
@use "scrollbar";

 // custom calendar today cell color
.calendar-cell.today {
  --cui-calendar-cell-today-color: var(--cui-info) !important;
}

// Sidebar Color Mixins (based on zen-logistic implementation)
@mixin set-background-color($color) {
  background-color: $color;
  @include set-sidebar-background($color);
  @include set-font-icon-color($color);
}

@mixin set-sidebar-background($color) {
  $overlay-opacity: 0.9;
  @if $color == #000000 {
    $overlay-opacity: 0.8;
  }

  .sidebar-background {
    &:after {
      background: $color;
      opacity: $overlay-opacity;
    }
  }
}

@mixin set-gradient-background-color($start-color, $end-color) {
  background: linear-gradient(45deg, $start-color, $end-color);
  $overlay-opacity: 0.9;

  .sidebar-background {
    &:after {
      background: linear-gradient(45deg, $start-color, $end-color);
      opacity: $overlay-opacity;
    }
  }
  @include set-font-icon-color($start-color);
}

@mixin set-font-icon-color($color) {
  $sidebar-font-color: #ffffff;
  @if $color == #ffffff {
    $sidebar-font-color: #000000;
  }

  .sidebar-nav .nav-link,
  .sidebar-nav .nav-link i,
  .sidebar-brand {
    color: $sidebar-font-color !important;
  }
}

// Theme Customizer Gradient Variables
$bg-hibiscus: linear-gradient(to right bottom, #f05f57, #c83d5c, #99245a, #671351, #360940);
$bg-purple-pizzazz: linear-gradient(to right bottom, #662d86, #8b2a8a, #ae2389, #cf1d83, #ed1e79);
$bg-blue-lagoon: linear-gradient(to right bottom, #144e68, #006d83, #008d92, #00ad91, #57ca85);
$bg-electric-violet: linear-gradient(to left top, #4a00e0, #600de0, #7119e1, #8023e1, #8e2de2);
$bg-portage: linear-gradient(to left top, #97abff, #798ce5, #5b6ecb, #3b51b1, #123597);
$bg-tundora: linear-gradient(to left top, #474747, #4a4a4a, #4c4d4d, #4f5050, #525352);

// Theme Background Classes
.bg-hibiscus {
  background-image: $bg-hibiscus;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-purple-pizzazz {
  background-image: $bg-purple-pizzazz;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-blue-lagoon {
  background-image: $bg-blue-lagoon;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-electric-violet {
  background-image: $bg-electric-violet;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-portage {
  background-image: $bg-portage;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-tundora {
  background-image: $bg-tundora;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}



// Icon animation class
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// custom select week cursor pointer
.select-week .calendar-row.current {
  cursor: pointer;
}

// Sidebar Data Attribute Color Styling
.sidebar {
  position: relative;

  // Add sidebar background element for overlays
  .sidebar-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      transition: all 0.3s ease;
    }
  }

  // Solid background colors
  &[data-background-color="white"] {
    @include set-background-color(#ffffff);
  }

  &[data-background-color="black"] {
    @include set-background-color(#000000);
  }

  &[data-background-color="primary"] {
    @include set-background-color(var(--cui-primary));
  }

  // Gradient background colors (matching zen-logistic)
  &[data-background-color="pomegranate"] {
    @include set-gradient-background-color(#9B3cb7, #FF396f);
  }

  &[data-background-color="king-yna"] {
    @include set-gradient-background-color(#1a2a6c, #b21f1f);
  }

  &[data-background-color="ibiza-sunset"] {
    @include set-gradient-background-color(#ee0979, #ff6a00);
  }

  &[data-background-color="flickr"] {
    @include set-gradient-background-color(#33001b, #ff0084);
  }

  &[data-background-color="purple-bliss"] {
    @include set-gradient-background-color(#360033, #0b8793);
  }

  &[data-background-color="man-of-steel"] {
    @include set-gradient-background-color(#780206, #061161);
  }

  &[data-background-color="purple-love"] {
    @include set-gradient-background-color(#cc2b5e, #753a88);
  }
}

// Gradient color classes for customizer color picker circles
.gradient-pomegranate {
  background: linear-gradient(45deg, #9B3cb7, #FF396f);
}

.gradient-king-yna {
  background: linear-gradient(45deg, #1a2a6c, #b21f1f);
}

.gradient-ibiza-sunset {
  background: linear-gradient(45deg, #ee0979, #ff6a00);
}

.gradient-flickr {
  background: linear-gradient(45deg, #33001b, #ff0084);
}

.gradient-purple-bliss {
  background: linear-gradient(45deg, #360033, #0b8793);
}

.gradient-man-of-steel {
  background: linear-gradient(45deg, #780206, #061161);
}

.gradient-purple-love {
  background: linear-gradient(45deg, #cc2b5e, #753a88);
}
