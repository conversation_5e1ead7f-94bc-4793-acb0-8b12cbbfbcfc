<!--Theme customizer Starts-->
<!-- Backdrop overlay -->
<div class="customizer-backdrop" [ngClass]="{show: isOpen}" (click)="closeCustomizer()"></div>

<div #customizer class="customizer border-left-blue-grey border-left-lighten-4" [ngClass]="{open: isOpen}">
  <a class="customizer-close" (click)="closeCustomizer()">
    <svg cIcon name="cilX" size="lg"></svg>
  </a>
  <div class="customizer-content p-3 ps-container ps-theme-dark text-left">
    <h4 class="text-uppercase mb-0 text-bold-400">Theme Customizer</h4>
    <p>Customize &amp; Preview in Real Time</p>
    <hr>



    <!-- Sidebar Options Starts-->
    <h6 class="text-center text-bold-500 mb-3 text-uppercase sb-options">Sidebar Color Options</h6>
    <div class="cz-bg-color sb-color-options">
      <div class="row p-1">
        <div class="col">
          <button type="button"
                  class="gradient-pomegranate d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="pomegranate"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.POMEGRANATE}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.POMEGRANATE)"
                  [attr.aria-label]="'Select pomegranate color theme'"
                  [attr.title]="'Pomegranate Theme'">
          </button>
        </div>
        <div class="col">
          <button type="button"
                  class="gradient-king-yna d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="king-yna"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.KING_YNA}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.KING_YNA)"
                  [attr.aria-label]="'Select king yna color theme'"
                  [attr.title]="'King Yna Theme'">
          </button>
        </div>
        <div class="col">
          <button type="button"
                  class="gradient-ibiza-sunset d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="ibiza-sunset"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.IBIZA_SUNSET}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.IBIZA_SUNSET)"
                  [attr.aria-label]="'Select ibiza sunset color theme'"
                  [attr.title]="'Ibiza Sunset Theme'">
          </button>
        </div>
        <div class="col">
          <button type="button"
                  class="gradient-flickr d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="flickr"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.FLICKR}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.FLICKR)"
                  [attr.aria-label]="'Select flickr color theme'"
                  [attr.title]="'Flickr Theme'">
          </button>
        </div>
        <div class="col">
          <button type="button"
                  class="gradient-purple-bliss d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="purple-bliss"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.PURPLE_BLISS}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.PURPLE_BLISS)"
                  [attr.aria-label]="'Select purple bliss color theme'"
                  [attr.title]="'Purple Bliss Theme'">
          </button>
        </div>
        <div class="col">
          <button type="button"
                  class="gradient-man-of-steel d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="man-of-steel"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.MAN_OF_STEEL}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.MAN_OF_STEEL)"
                  [attr.aria-label]="'Select man of steel color theme'"
                  [attr.title]="'Man of Steel Theme'">
          </button>
        </div>
        <div class="col">
          <button type="button"
                  class="gradient-purple-love d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="purple-love"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.PURPLE_LOVE}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.PURPLE_LOVE)"
                  [attr.aria-label]="'Select purple love color theme'"
                  [attr.title]="'Purple Love Theme'">
          </button>
        </div>
      </div>
      <div class="row p-1">
        <div class="col">
          <button type="button"
                  class="bg-black d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="black"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.BLACK}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.BLACK)"
                  [attr.aria-label]="'Select black color theme'"
                  [attr.title]="'Black Theme'">
          </button>
        </div>
        <div class="col">
          <button type="button"
                  class="bg-grey d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="white"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.WHITE}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.WHITE)"
                  [attr.aria-label]="'Select white color theme'"
                  [attr.title]="'White Theme'">
          </button>
        </div>
        <div class="col">
          <button type="button"
                  class="bg-primary d-block rounded-circle border-0 color-option-btn"
                  style="width:24px; height:24px; cursor: pointer;"
                  data-bg-color="primary"
                  [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.PRIMARY}"
                  (click)="changeSidebarBgColor(SIDEBAR_COLORS.PRIMARY)"
                  [attr.aria-label]="'Select primary color theme'"
                  [attr.title]="'Primary Theme'">
          </button>
        </div>
      </div>
    </div>
    <!-- Sidebar Options Ends-->


    <hr />

    <!--Sidebar BG Image Toggle Starts-->
    <div class="togglebutton toggle-sb-bg-img">
      <div class="switch switch border-0 d-flex justify-content-between w-100">
        <span>Sidebar Bg Image</span>
        <div class="float-right">
          <div class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0">
            <input type="checkbox" class="custom-control-input cz-bg-image-display"
                   [checked]="isBgImageDisplay" id="sidebar-bg-img" (change)="bgImageDisplay($event)">
            <label class="custom-control-label d-block" for="sidebar-bg-img"></label>
          </div>
        </div>
      </div>
      <hr>
    </div>
    <!--Sidebar BG Image Toggle Ends-->

    <!--Compact Menu Starts-->
    <div class="togglebutton">
      <div class="switch switch border-0 d-flex justify-content-between w-100">
        <span>Compact Menu</span>
        <div class="float-right">
          <div class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0">
            <input type="checkbox" [checked]="config.layout.sidebar.collapsed"
                   class="custom-control-input cz-compact-menu" id="cz-compact-menu" (change)="toggleCompactMenu($event)">
            <label class="custom-control-label d-block" for="cz-compact-menu"></label>
          </div>
        </div>
      </div>
    </div>
    <!--Compact Menu Ends-->
    <hr>

    <!--Sidebar Width Starts-->
    <div>
      <label for="cz-sidebar-width">Sidebar Width</label>
      <select id="cz-sidebar-width" #width class="custom-select cz-sidebar-width float-right" (change)="changeSidebarWidth(width.value)">
        <option value="sidebar-sm" [selected]="size === 'sidebar-sm'">Small</option>
        <option value="sidebar-md" [selected]="size === 'sidebar-md'">Medium</option>
        <option value="sidebar-lg" [selected]="size === 'sidebar-lg'">Large</option>
      </select>
    </div>
    <!--Sidebar Width Ends-->
  </div>
</div>
<!--Theme customizer Ends-->
