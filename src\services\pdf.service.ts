import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class PdfService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Upload PDF file
   * @param file - PDF file
   * @param fileName - File name
   * @returns Observable with upload result
   */
  uploadPdf(file: File, fileName: string): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('pdfAttachment', file, fileName);
    return this.http.post<any>(`${this.apiURL}pdf/upload-pdf`, formData, httpOptions);
  }

  /**
   * Generate PDF from HTML
   * @param htmlContent - HTML content
   * @returns Observable with PDF generation result
   */
  generatePdfFromHtml(htmlContent: string): Observable<any> {
    const data = { htmlContent };
    return this.http.post<any>(`${this.apiURL}pdf/generate`, data, httpOptions);
  }

  /**
   * Download PDF
   * @param pdfId - PDF ID
   * @returns Observable with PDF download
   */
  downloadPdf(pdfId: string): Observable<Blob> {
    return this.http.get(`${this.apiURL}pdf/download/${pdfId}`, {
      ...httpOptions,
      responseType: 'blob'
    });
  }
}
