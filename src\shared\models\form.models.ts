export interface FormField {
  type: 'text' | 'number' | 'email' | 'password' | 'textarea' | 'select' | 'multiselect' | 'image';
  name: string;
  label: string;
  placeholder?: string;
  required?: boolean;
  defaultValue?: any;
  validation?: (value: any) => string | null;
  
  // For select/multiselect
  options?: any[]; // Array of objects
  optionValue?: string; // Property to use as value
  optionLabel?: string; // Property to use as label
  
  // For image upload
  multipleImage?: boolean; // Whether to allow multiple image uploads
  accept?: string; // Comma-separated list of accepted file types (e.g. "image/*,.pdf")
}

export interface FormAction {
  label: string;
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  icon?: string;
  onClick: (formData: any) => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface FormConfig {
  title?: string;
  fields: FormField[];
  actions: FormAction[];
  layout?: 'vertical' | 'horizontal';
}