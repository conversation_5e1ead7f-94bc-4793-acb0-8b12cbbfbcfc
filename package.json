{"name": "coreui-free-angular-admin-template", "version": "5.5.1", "copyright": "Copyright 2024 creativeL<PERSON><PERSON><PERSON>", "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people) and contributors", "homepage": "https://coreui.io/angular", "config": {"theme": "default", "coreui_library_short_version": "5.5", "coreui_library_docs_url": "https://coreui.io/angular/docs/"}, "scripts": {"ng": "ng", "start": "ng serve -o", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^20.0.3", "@angular/cdk": "^20.0.3", "@angular/common": "^20.0.3", "@angular/compiler": "^20.0.3", "@angular/core": "^20.0.3", "@angular/forms": "^20.0.3", "@angular/language-service": "^20.0.3", "@angular/localize": "^20.0.3", "@angular/platform-browser": "^20.0.3", "@angular/platform-browser-dynamic": "^20.0.3", "@angular/router": "^20.0.3", "@coreui/angular": "~5.5.1", "@coreui/angular-chartjs": "~5.5.1", "@coreui/chartjs": "~4.1.0", "@coreui/coreui": "~5.4.0", "@coreui/icons": "^3.0.1", "@coreui/icons-angular": "~5.5.1", "@coreui/utils": "^2.0.2", "chart.js": "^4.5.0", "lodash-es": "^4.17.21", "ngx-scrollbar": "^13.0.3", "rxjs": "~7.8.2", "tslib": "^2.8.1", "zone.js": "~0.15.1"}, "devDependencies": {"@angular/build": "^20.0.2", "@angular/cli": "^20.0.2", "@angular/compiler-cli": "^20.0.3", "@types/jasmine": "^5.1.8", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.32", "jasmine-core": "^5.8.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "~5.8.3"}, "engines": {"node": "^20.19.0 || ^22.12.0 || ^24.0.0", "npm": ">= 9"}, "bugs": {"url": "https://github.com/coreui/coreui-free-angular-admin-template/issues"}, "repository": {"type": "git", "url": "git+https://github.com/coreui/coreui-free-angular-admin-template.git"}}