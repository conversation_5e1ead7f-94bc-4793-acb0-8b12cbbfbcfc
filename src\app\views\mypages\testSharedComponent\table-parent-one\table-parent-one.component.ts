import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableColumn, TableAction, TableConfig, ActionButton } from '../../../../../shared/models/table.models';
import { SmartTableComponent } from '../../../../../shared/components/smart-table/smart-table.component';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';
import { IconModule } from '@coreui/icons-angular';

@Component({
  standalone: true,
  selector: 'app-table-parent-one',
  templateUrl: './table-parent-one.component.html',
  styleUrls: ['./table-parent-one.component.scss'],
  imports: [SmartContainerComponent,SmartTableComponent,CommonModule,IconModule]
})
export class TableParentOneComponent {
  // Table configuration
  tableColumns: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true, dataType: 'number' },
    { name: 'name', displayName: 'Name', sortable: true },
    { name: 'email', displayName: 'Email', sortable: false },
    { name: 'createdAt', displayName: 'Join Date', sortable: true, dataType: 'date' },
    { name: 'active', displayName: 'Active', sortable: true, dataType: 'boolean' },
    {
      name: 'status',
      displayName: 'Status',
      sortable: true,
      dataType: 'status',
      statusConfig: [
        { value: 'pending', displayText: 'Pending Review', badgeColor: 'warning', icon: 'cilClock' },
        { value: 'approved', displayText: 'Approved', badgeColor: 'success', icon: 'cilCheckCircle' },
        { value: 'rejected', displayText: 'Rejected', badgeColor: 'danger', icon: 'cilXCircle' },
        { value: 'in-review', displayText: 'In Review', badgeColor: 'info', icon: 'cilSearch' }
      ]
    },
    {
      name: 'priority',
      displayName: 'Priority',
      sortable: true,
      dataType: 'status',
      statusConfig: [
        { value: 'low', displayText: 'Low', badgeColor: 'secondary', customClasses: 'text-lowercase' },
        { value: 'medium', displayText: 'Medium', badgeColor: 'primary' },
        { value: 'high', displayText: 'High', badgeColor: 'warning', icon: 'cilWarning' },
        { value: 'critical', displayText: 'Critical', badgeColor: 'danger', icon: 'cilBell', customClasses: 'fw-bold' }
      ]
    }
  ];

  tableData = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', createdAt: '2023-01-15', active: true, status: 'approved', priority: 'high' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', createdAt: '2023-02-20', active: false, status: 'rejected', priority: 'low' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', createdAt: '2023-03-10', active: true, status: 'pending', priority: 'medium' },
    { id: 4, name: 'Alice Brown', email: '<EMAIL>', createdAt: '2023-04-05', active: true, status: 'in-review', priority: 'critical' },
    { id: 5, name: 'Charlie Davis', email: '<EMAIL>', createdAt: '2023-05-12', active: false, status: 'approved', priority: 'medium' },
    { id: 6, name: 'Eva Wilson', email: '<EMAIL>', createdAt: '2023-06-18', active: true, status: 'pending', priority: 'low' },
    { id: 7, name: 'Mike Taylor', email: '<EMAIL>', createdAt: '2023-07-22', active: true, status: 'rejected', priority: 'high' },
    { id: 8, name: 'Sarah Connor', email: '<EMAIL>', createdAt: '2023-08-14', active: false, status: 'in-review', priority: 'critical' }
  ];

  tableActions: TableAction[] = [
    {
      name: 'edit',
      icon: 'cil-pencil',
      color: 'primary',
      hidden: false,
      tooltip: 'Edit user'
    },
    {
      name: 'delete',
      icon: 'cil-trash',
      color: 'danger',
      condition: (row: any) => row.active,
      hidden: false,
      tooltip: 'Delete user'
    },
  ];

  // New action buttons with callback functions
  actionButtons: ActionButton[] = [
    {
      icon: 'cilZoom',
      color: 'info',
      tooltip: 'View Details',
      callback: (row: any) => this.viewUserDetails(row)
    },
    {
      label: 'Activate',
      icon: 'cilCheckCircle',
      color: 'success',
      tooltip: 'Activate user',
      condition: (row: any) => !row.active,
      callback: (row: any) => this.toggleUserStatus(row, true)
    },
    {
      label: 'Deactivate',
      icon: 'cilXCircle',
      color: 'warning',
      tooltip: 'Deactivate user',
      condition: (row: any) => row.active,
      callback: (row: any) => this.toggleUserStatus(row, false)
    },
    {
      label: 'Send Email',
      icon: 'cilEnvelopeClosed',
      color: 'secondary',
      tooltip: 'Send email to user',
      disabled: (row: any) => !row.email || row.email === '',
      callback: (row: any) => this.sendEmailToUser(row)
    }
  ];

  tableConfig: TableConfig = {
    pageSizeOptions: [5, 10, 25],
    pageSize: 5,
    selectable: true,
    multiSelect: true,
    emptyMessage: 'No users found',
    striped: true,
    hover: true,
    bordered: false,
    small: false,
    showFilter: true
  };

  loading = false;
  selectedRows: any[] = [];
  lastAction: string = '';

  // Handle table actions
  handleTableAction(event: {action: string, row: any}) {
    this.lastAction = `${event.action} (ID: ${event.row.id})`;
    console.log('Table Action:', event);
    
    switch(event.action) {
      case 'edit':
        this.editUser(event.row);
        break;
      case 'delete':
        this.deleteUser(event.row);
        break;
      case 'view':
        this.viewUser(event.row);
        break;
      default:
        console.warn('Unknown action:', event.action);
    }
  }

  // Handle row selection changes
  handleSelectionChange(selectedRows: any[]) {
    console.log('Selection Changed:', selectedRows);
    this.selectedRows = selectedRows;
  }

  // Get selected row IDs as a string
  getSelectedIds(): string {
    return this.selectedRows.map(row => row.id).join(', ');
  }

  // Action implementations
  editUser(user: any) {
    console.log('Editing user:', user);
    // In a real app, you would open a modal or navigate to edit page
    alert(`Editing user: ${user.name}\nID: ${user.id}`);
  }

  deleteUser(user: any) {
    console.log('Deleting user:', user);
    if (confirm(`Are you sure you want to delete ${user.name}?`)) {
      this.tableData = this.tableData.filter(u => u.id !== user.id);
      console.log('User deleted:', user);
    }
  }

  viewUser(user: any) {
    console.log('Viewing user:', user);
    alert(`User Details:\n\nID: ${user.id}\nName: ${user.name}\nEmail: ${user.email}\nActive: ${user.active ? 'Yes' : 'No'}\nStatus: ${user.status}\nPriority: ${user.priority}\nJoined: ${new Date(user.createdAt).toLocaleDateString()}`);
  }

  // Add a new user (demo method)
  addUser() {
    const newId = Math.max(...this.tableData.map(u => u.id)) + 1;
    const statuses = ['pending', 'approved', 'rejected', 'in-review'];
    const priorities = ['low', 'medium', 'high', 'critical'];
    const newUser = {
      id: newId,
      name: `New User ${newId}`,
      email: `user${newId}@example.com`,
      createdAt: new Date().toISOString(),
      active: true,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      priority: priorities[Math.floor(Math.random() * priorities.length)]
    };
    this.tableData = [...this.tableData, newUser];
    console.log('User added:', newUser);
  }

  // New callback methods for action buttons
  viewUserDetails(row: any) {
    this.lastAction = `View details for ${row.name} (ID: ${row.id})`;
    console.log('Viewing user details:', row);
    // Here you could open a modal, navigate to a detail page, etc.
    alert(`Viewing details for ${row.name}\nEmail: ${row.email}\nStatus: ${row.status}\nPriority: ${row.priority}`);
  }

  toggleUserStatus(row: any, active: boolean) {
    const action = active ? 'activated' : 'deactivated';
    this.lastAction = `User ${row.name} ${action}`;

    // Update the user in the data array
    const userIndex = this.tableData.findIndex(u => u.id === row.id);
    if (userIndex !== -1) {
      this.tableData[userIndex] = { ...this.tableData[userIndex], active };
      // Trigger change detection by creating a new array reference
      this.tableData = [...this.tableData];
    }

    console.log(`User ${action}:`, row);
  }

  sendEmailToUser(row: any) {
    this.lastAction = `Email sent to ${row.name}`;
    console.log('Sending email to user:', row);
    // Here you could integrate with an email service
    alert(`Email sent to ${row.name} at ${row.email}`);
  }

  // Refresh data (demo method)
  refreshData() {
    this.loading = true;
    console.log('Refreshing data...');
    // Simulate API call
    setTimeout(() => {
      this.loading = false;
      console.log('Data refreshed');
    }, 1000);
  }
}