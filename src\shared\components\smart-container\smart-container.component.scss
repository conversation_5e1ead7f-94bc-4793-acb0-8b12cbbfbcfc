.c-card {
  border: 1px solid var(--cui-border-color);
  border-radius: var(--cui-border-radius);
  box-shadow: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.075);
  
  .c-card-header {
    background-color: var(--cui-card-cap-bg);
    border-bottom: 1px solid var(--cui-border-color);
    padding: 1rem 1.25rem;
    
    .header-content {
      h4 {
        color: var(--cui-body-color);
        font-weight: 600;
        font-size: 1.25rem;
      }
      
      p {
        font-size: 0.875rem;
        color: var(--cui-text-muted);
      }
    }
    
    .header-actions {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      
      // Responsive adjustments
      @media (max-width: 576px) {
        flex-direction: column;
        gap: 0.25rem;
        
        .btn {
          font-size: 0.875rem;
          padding: 0.375rem 0.75rem;
        }
      }
    }
    
    // Mobile responsive header
    @media (max-width: 768px) {
      .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
        
        .header-actions {
          align-self: stretch;
          justify-content: flex-end;
        }
      }
    }
  }
  
  .c-card-body {
    padding: 1.25rem;
    background-color: var(--cui-card-bg);
    
    // Remove default margins from first and last children
    > :first-child {
      margin-top: 0;
    }
    
    > :last-child {
      margin-bottom: 0;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .c-card {
    background-color: var(--cui-dark);
    border-color: var(--cui-border-color-dark);
    
    .c-card-header {
      background-color: var(--cui-dark-bg-subtle);
      border-bottom-color: var(--cui-border-color-dark);
      
      .header-content h4 {
        color: var(--cui-light);
      }
    }
    
    .c-card-body {
      background-color: var(--cui-dark);
    }
  }
}
