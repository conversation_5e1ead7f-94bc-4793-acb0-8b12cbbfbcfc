import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class TruckService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Add new truck
   * @param truckData - Truck data
   * @returns Observable with creation result
   * @originalName addCamion
   */
  addTruck(truckData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'camion', truckData, httpOptions);
  }

  /**
   * Get all trucks
   * @returns Observable with trucks list
   * @originalName findAllCamion
   */
  getAllTrucks(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'camion', httpOptions);
  }

  /**
   * Get trucks by transporter
   * @param id - Transporter ID
   * @returns Observable with trucks list
   * @originalName getCustomerDirectly
   */
  getTrucksByTransporter(id: string): Observable<any> {
    return this.http.get(this.apiURL + 'camionTr/' + id, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Delete truck
   * @param id - Truck ID
   * @returns Observable with deletion result
   * @originalName deleteCamion
   */
  deleteTruck(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'camion/delate/' + id, httpOptions);
  }

  /**
   * Find truck by ID
   * @param id - Truck ID
   * @returns Observable with truck data
   * @originalName findcamion
   */
  findTruckById(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'camion/' + id, httpOptions);
  }

  /**
   * Get all trucks (admin view)
   * @returns Observable with trucks list
   */
  getAllTrucksAdmin(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'camion', httpOptions);
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
