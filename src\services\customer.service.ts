import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class CustomerService {
  private apiURL = environment.apiURL;

  constructor(
    private http: HttpClient,
    private router: Router
  ) { }

  /**
   * Get all customers
   * @returns Observable with customers list
   * @originalName listCustomers, findAll
   */
  getAllCustomers(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customers', httpOptions);
  }

  /**
   * Add new customer
   * @param customerData - Customer data
   * @returns Observable with creation result
   * @originalName addCustomers
   */
  addCustomer(customerData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'customers', customerData, httpOptions);
  }

  /**
   * Find customer by ID
   * @param id - Customer ID
   * @returns Observable with customer data
   * @originalName findOne
   */
  findCustomerById(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customers/' + id, httpOptions)
      .pipe(
        catchError(error => {
          if (error.status === 401 || error.status === 403 || error.status === 0) {
            this.router.navigate(['pages/login']);
          }
          return throwError(() => error);
        })
      );
  }

  /**
   * Update customer
   * @param customerData - Updated customer data
   * @param id - Customer ID
   * @returns Observable with update result
   */
  updateCustomer(customerData: any, id: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'cust/' + id, customerData, httpOptions);
  }

  /**
   * Update customer by key
   * @param customerData - Updated customer data
   * @param key - Customer key
   * @returns Observable with update result
   */
  updateCustomerByKey(customerData: any, key: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'customers/update/' + key, customerData, httpOptions);
  }

  /**
   * Update customer for transporter
   * @param customerData - Updated customer data
   * @param id - Customer ID
   * @returns Observable with update result
   */
  updateCustomerTransporter(customerData: any, id: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'custumm/' + id, customerData, httpOptions);
  }

  /**
   * Update customer transporter info
   * @param customerData - Updated customer data
   * @param id - Customer ID
   * @returns Observable with update result
   */
  updateTransporter(customerData: any, id: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'custom/' + id, customerData, httpOptions);
  }

  /**
   * Delete customer
   * @param id - Customer ID
   * @returns Observable with deletion result
   */
  deleteCustomer(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customers/delate/' + id, httpOptions);
  }

  /**
   * Get all transporters
   * @returns Observable with transporters list
   */
  getAllTransporters(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customer/Transporteur', httpOptions);
  }

  /**
   * Get all expeditors
   * @returns Observable with expeditors list
   */
  getAllExpeditors(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customer/Expediteur', httpOptions);
  }

  /**
   * Get clients by expeditor
   * @param expeditorId - Expeditor ID
   * @returns Observable with clients list
   */
  getClientsByExpeditor(expeditorId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'getClientByExp/' + expeditorId, httpOptions);
  }

  /**
   * Get all clients
   * @returns Observable with clients list
   */
  getAllClients(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'getAllClients', httpOptions);
  }

  /**
   * Set client to expeditor
   * @param clientId - Client ID
   * @param expeditorId - Expeditor ID
   * @returns Observable with assignment result
   */
  setClientToExpeditor(clientId: string, expeditorId: string): Observable<any> {
    const data = {
      idExp: expeditorId,
      idClient: clientId
    };
    return this.http.post<any>(this.apiURL + 'customers/setclient', data, httpOptions);
  }

  /**
   * Remove client from expeditor
   * @param clientId - Client ID
   * @param expeditorId - Expeditor ID
   * @returns Observable with removal result
   */
  removeClientFromExpeditor(clientId: string, expeditorId: string): Observable<any> {
    const data = {
      idExp: expeditorId,
      idClient: clientId
    };
    return this.http.post<any>(this.apiURL + 'customers/removeClient', data, httpOptions);
  }

  /**
   * Disable customer
   * @param customerId - Customer ID
   * @returns Observable with disable result
   */
  disableCustomer(customerId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `customers/disable/${customerId}`, httpOptions);
  }

  /**
   * Update customer color
   * @param id - Customer ID
   * @param colorData - Color data
   * @returns Observable with update result
   */
  updateCustomerColor(id: string, colorData: any): Observable<any> {
    return this.http.put<any>(this.apiURL + 'updateClientColor/' + id, colorData, httpOptions);
  }

  /**
   * Get discharge locations by customer
   * @param clientId - Client ID
   * @returns Observable with locations list
   */
  getDischargeLocationsByCustomer(clientId: string): Observable<any> {
    const data = { user_id: clientId };
    return this.http.post<any>(this.apiURL + 'getLienuxDecharByCustomer', data, httpOptions);
  }

  /**
   * Add discharge location
   * @param clientId - Client ID
   * @param label - Location label
   * @returns Observable with addition result
   */
  addDischargeLocation(clientId: string, label: string): Observable<any> {
    const data = {
      user_id: clientId,
      libelle: label
    };
    return this.http.post<any>(this.apiURL + 'addLieuxDechar', data, httpOptions);
  }
}
