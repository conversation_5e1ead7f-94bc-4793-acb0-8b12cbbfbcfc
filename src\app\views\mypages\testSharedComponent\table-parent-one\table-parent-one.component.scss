.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  background-color: var(--cui-body-bg);
  color: var(--cui-body-color);
}

.status-bar {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  .alert {
    margin: 0;
    padding: 0.75rem 1rem;
    border-radius: var(--cui-border-radius);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid;
    font-size: 0.875rem;

    c-icon {
      font-size: 1rem;
    }
  }

  .alert-info {
    color: var(--cui-info-text-emphasis);
    background-color: var(--cui-info-bg-subtle);
    border-color: var(--cui-info-border-subtle);
  }

  .alert-success {
    color: var(--cui-success-text-emphasis);
    background-color: var(--cui-success-bg-subtle);
    border-color: var(--cui-success-border-subtle);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
}