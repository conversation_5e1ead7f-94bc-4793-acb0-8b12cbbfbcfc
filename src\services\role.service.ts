import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { AuthService } from './auth.service';
import { User } from '../models/auth.interfaces';

@Injectable({
  providedIn: 'root'
})
export class RoleService {

  constructor(private authService: AuthService) { }

  /**
   * Get current user's role synchronously
   */
  getRole(): string | null {
    const user = this.authService.getUser();
    return user ? (user.role || user.type_utilisateur || null) : null;
  }

  /**
   * Get current user's role as Observable
   */
  getRole$(): Observable<string | null> {
    return this.authService.currentUser$.pipe(
      map(user => user ? (user.role || user.type_utilisateur || null) : null)
    );
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    const userRole = this.getRole();
    return userRole === role;
  }

  /**
   * Check if user has any of the specified roles
   */
  hasAnyRole(roles: string[]): boolean {
    const userRole = this.getRole();
    return roles.some(role => userRole === role);
  }

  /**
   * Check if user has all of the specified roles
   */
  hasAllRoles(roles: string[]): boolean {
    const userRole = this.getRole();
    // For single role system, user can only have all roles if there's only one required role
    return roles.length === 1 && roles[0] === userRole;
  }

  /**
   * Check role access as Observable
   */
  hasRole$(role: string): Observable<boolean> {
    return this.getRole$().pipe(
      map(userRole => userRole === role)
    );
  }

  /**
   * Check if user has any of the specified roles as Observable
   */
  hasAnyRole$(roles: string[]): Observable<boolean> {
    return this.getRole$().pipe(
      map(userRole => roles.some(role => userRole === role))
    );
  }

  /**
   * Get all available roles (this would typically come from a backend service)
   */
  getAvailableRoles(): string[] {
    return [
      'SuperAdmin',
      'Administrateur',
      'Client',
      'Chef Departement',
      'GS',
      'GE',
      'Depot',
      'Inspection',
      'FACTURATION',
      'MAGASIN',
      'Conducteur'
    ];
  }

  /**
   * Check if a role exists in the system
   */
  isValidRole(role: string): boolean {
    return this.getAvailableRoles().includes(role);
  }
}
