<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Pagination</strong>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          We use a large block of connected links for our pagination, making links hard to miss
          and easily scalable—all while providing large hit areas. Pagination is built with list
          HTML elements so screen readers can announce the number of available links. Use a
          wrapping <code>&lt;nav&gt;</code> element to identify it as a navigation section to
          screen readers and other assistive technologies.
        </p>
        <p class="text-body-secondary small">
          In addition, as pages likely have more than one such navigation section, it&#39;s
          advisable to provide a descriptive <code>aria-label</code> for the
          <code>&lt;nav&gt;</code> to reflect its purpose. For example, if the pagination
          component is used to navigate between a set of search results, an appropriate label
          could be <code>aria-label=&#34;Search results pages&#34;</code>.
        </p>
        <app-docs-example href="components/pagination">
          <c-pagination aria-label="Page navigation example">
            <c-page-item>
              <a cPageLink [routerLink]="[]">Previous</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">1</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">2</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">3</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">Next</a>
            </c-page-item>
          </c-pagination>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Pagination</strong> <small>Working with icons</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Looking to use an icon or symbol in place of text for some pagination links? Be sure
          to provide proper screen reader support with <code>aria</code> attributes.
        </p>
        <app-docs-example href="components/pagination#working-with-icons">
          <c-pagination aria-label="Page navigation example">
            <c-page-item aria-label="Previous">
              <a cPageLink [routerLink]="[]"><span aria-hidden="true">&laquo;</span></a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">1</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">2</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">3</a>
            </c-page-item>
            <c-page-item aria-label="Next">
              <a cPageLink [routerLink]="[]"><span aria-hidden="true">&raquo;</span></a>
            </c-page-item>
          </c-pagination>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Pagination</strong> <small>Disabled and active states</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Pagination links are customizable for different circumstances. Use
          <code>disabled</code> for links that appear un-clickable and <code>.active</code> to
          indicate the current page.
        </p>
        <p class="text-body-secondary small">
          While the <code>disabled</code> prop uses <code>pointer-events: none</code> to
          <em>try</em> to disable the link functionality of <code>&lt;a&gt;</code>s, that CSS
          property is not yet standardized and doesn&#39;taccount for keyboard navigation. As
          such, we always add <code>tabindex=&#34;-1&#34;</code> on disabled links and use
          custom JavaScript to fully disable their functionality.
        </p>
        <app-docs-example href="components/pagination#disabled-and-active-states">
          <c-pagination aria-label="Page navigation example">
            <c-page-item [disabled]="true" aria-label="Previous">
              <a cPageLink [routerLink]="[]"><span aria-hidden="true">&laquo;</span></a>
            </c-page-item>
            <c-page-item [active]="true">
              <a [routerLink]="[]" cPageLink>1</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">2</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">3</a>
            </c-page-item>
            <c-page-item aria-label="Next">
              <a cPageLink [routerLink]="[]"><span aria-hidden="true">&raquo;</span></a>
            </c-page-item>
          </c-pagination>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Pagination</strong> <small>Sizing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Fancy larger or smaller pagination? Add <code>sizing=&#34;lg&#34;</code> or
          <code>sizing=&#34;sm&#34;</code> for additional sizes.
        </p>
        <app-docs-example href="components/pagination#sizing">
          <c-pagination aria-label="Page navigation example" size="lg">
            <c-page-item>
              <a cPageLink [routerLink]="[]">Previous</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">1</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">2</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">3</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">Next</a>
            </c-page-item>
          </c-pagination>
        </app-docs-example>
        <app-docs-example href="components/pagination#sizing">
          <c-pagination aria-label="Page navigation example" size="sm">
            <c-page-item>
              <a cPageLink [routerLink]="[]">Previous</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">1</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">2</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">3</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">Next</a>
            </c-page-item>
          </c-pagination>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Pagination</strong> <small>Alignment</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Change the alignment of pagination components with
          <a href="https://coreui.io/docs/utilities/flex/">flexbox utilities</a>.
        </p>
        <app-docs-example href="components/pagination#aligment">
          <c-pagination align="center" aria-label="Page navigation example">
            <c-page-item [disabled]="true">
              <a cPageLink [routerLink]="[]">Previous</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">1</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">2</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">3</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">Next</a>
            </c-page-item>
          </c-pagination>
        </app-docs-example>
        <app-docs-example href="components/pagination#aligment">
          <c-pagination align="end" aria-label="Page navigation example">
            <c-page-item [disabled]="true">
              <a cPageLink [routerLink]="[]">Previous</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">1</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">2</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">3</a>
            </c-page-item>
            <c-page-item>
              <a cPageLink [routerLink]="[]">Next</a>
            </c-page-item>
          </c-pagination>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
