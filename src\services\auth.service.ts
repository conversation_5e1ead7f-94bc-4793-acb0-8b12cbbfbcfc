import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '../environments/environment';
import { BehaviorSubject, catchError, Observable, tap, throwError } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiURL = environment.apiURL;
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  private userSubject = new BehaviorSubject<{iduser:number, email: string; role: string } | null>(null);
  user$ = this.userSubject.asObservable();

  constructor(private http: HttpClient,private router: Router) { }


  login(data:any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'customers/login/', data , httpOptions).pipe(
      tap((response:any) => {
        console.log(response);
        if (response && response.token) {
          this.isAuthenticatedSubject.next(true);
          this.userSubject.next({iduser:response.id, email: response.email, role: response.role });
        }}),
      catchError((err) => {
        this.isAuthenticatedSubject.next(false);
        this.userSubject.next(null);
        return throwError(() => new Error('Invalid credentials'));
      })
    );
  }
  logout(): void {
    this.isAuthenticatedSubject.next(false);
    this.userSubject.next(null);
    //this.router.navigate(['pages/login']);
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  getUser(): { email: string; role: string } | null {
    return this.userSubject.value;
  }


  register(userData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'customers', userData, httpOptions);
  }


  updateLastConnection(id: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'lastConnexion/' + id, null, httpOptions);
  }


  sendActivationEmail(emailData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'email', emailData, httpOptions);
  }

  findUserByEmail(email: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `getUserByMail/${email}`, httpOptions);
  }


  verifyUsername(username: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'custo/' + username, httpOptions);
  }

  handleAuthError(error: any): void {
    if (error.status === 401 || error.status === 403 || error.status === 0) {
      this.router.navigate(['pages/login']);
    }
  }


  getAllRoles(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'roles', httpOptions);
  }


  updateUserInfo(userData: any): Observable<any> {
    return this.http.put<any>(this.apiURL + 'updateUserById/' + userData.id, userData, httpOptions);
  }


  createChefUser(userData: any): Promise<any> {
    return this.http.post<any>(this.apiURL + 'createChefUser', userData, httpOptions)
      .toPromise();
  }

  findUsersByChef(chefId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `findUsersByChef/${chefId}`, httpOptions);
  }


  findAllChefs(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findAllChef', httpOptions);
  }


  findAllClients(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findAllClient', httpOptions);
  }
}
