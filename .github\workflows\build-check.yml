name: Build Check

on:
  push:
    branches-ignore:
      - "dependabot/**"
  pull_request:
    branches:
      - main
      - v5.*
  schedule:
    - # build runs every weekday at 4:15AM UTC
    - cron: '15 4 * * *'

env:
  FORCE_COLOR: 2
  NODE: 22

jobs:
  build:
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest, macOS-latest]
        node-version: [22.x]
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.node-version }}

      - name: Install npm dependencies
        run: npm ci

      - name: Run build
        run: npm run build
