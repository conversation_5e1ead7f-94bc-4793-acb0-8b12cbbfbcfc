<div class="dynamic-form" [class.horizontal]="config.layout === 'horizontal'">
  <h2 *ngIf="config.title">{{ config.title }}</h2>
  
  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="form-fields">
      <div *ngFor="let field of config.fields" class="form-field">
        <!-- Text Inputs -->
        <div *ngIf="['text','number','email','password'].includes(field.type)" class="form-group">
          <label [for]="field.name">{{ field.label }}<span *ngIf="field.required">*</span></label>
          <input 
            [type]="field.type" 
            [id]="field.name"
            [formControlName]="field.name"
            [placeholder]="field.placeholder || ''">
          <div *ngIf="form.get(field.name)?.touched && form.get(field.name)?.errors" class="error-message">
            This field is required
          </div>
        </div>

        <!-- Textarea -->
        <div *ngIf="field.type === 'textarea'" class="form-group">
          <label [for]="field.name">{{ field.label }}<span *ngIf="field.required">*</span></label>
          <textarea 
            [id]="field.name"
            [formControlName]="field.name"
            [placeholder]="field.placeholder || ''"
            rows="4"></textarea>
          <div *ngIf="form.get(field.name)?.touched && form.get(field.name)?.errors" class="error-message">
            This field is required
          </div>
        </div>

        <!-- Select -->
        <div *ngIf="field.type === 'select'" class="form-group">
          <label [for]="field.name">{{ field.label }}<span *ngIf="field.required">*</span></label>
          <select
            [id]="field.name"
            [formControlName]="field.name"
            (change)="onSelectChange(field.name, $event)">
            <option value="" disabled selected>{{ field.placeholder || 'Select an option' }}</option>
            <option *ngFor="let option of getSelectOptions(field.name)"
                    [value]="getOptionValue(field.name, option)">
              {{ getOptionLabel(field.name, option) }}
            </option>
          </select>
          <div *ngIf="form.get(field.name)?.touched && form.get(field.name)?.errors" class="error-message">
            This field is required
          </div>
        </div>

        <!-- Multiselect -->
        <div *ngIf="field.type === 'multiselect'" class="form-group">
          <label>{{ field.label }}<span *ngIf="field.required">*</span></label>
          <div class="multiselect-options">
            <div *ngFor="let option of getSelectOptions(field.name)" class="multiselect-option">
              <input
                type="checkbox"
                [id]="field.name + '-' + getOptionValue(field.name, option)"
                [checked]="isSelected(field.name, getOptionValue(field.name, option))"
                (change)="onMultiSelectChange(field.name, getOptionValue(field.name, option), $event)">
              <label [for]="field.name + '-' + getOptionValue(field.name, option)">
                {{ getOptionLabel(field.name, option) }}
              </label>
            </div>
          </div>
          <div *ngIf="form.get(field.name)?.touched && form.get(field.name)?.errors" class="error-message">
            This field is required
          </div>
        </div>

        <!-- Image Upload -->
        <div *ngIf="field.type === 'image'" class="form-group">
          <label>{{ field.label }}<span *ngIf="field.required">*</span></label>
          <div class="image-upload-container">
            <input 
              type="file"
              [id]="field.name"
              [accept]="field.accept || 'image/*'"
              [multiple]="field.multipleImage || false"
              (change)="onFileChange($event, field.name)"
              style="display: none;">
            
            <label [for]="field.name" class="upload-button">
              <i class="cil-cloud-upload"></i> 
              {{ field.multipleImage ? 'Choose Files' : 'Choose File' }}
            </label>
            
            <div *ngIf="imagePreviews[field.name]" class="image-preview">
              <img [src]="imagePreviews[field.name]" alt="Preview">
              <button type="button" class="remove-image" (click)="removeImage(field.name)">
                <i class="cil-x"></i>
              </button>
            </div>
            
            <div *ngIf="form.get(field.name)?.touched && form.get(field.name)?.errors" class="error-message">
              This field is required
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <button 
        *ngFor="let action of config.actions"
        type="button"
        [class]="'btn btn-' + (action.color || 'primary')"
        (click)="onActionClick(action)">
        <i *ngIf="action.icon" [class]="action.icon"></i>
        {{ action.label }}
      </button>
    </div>
  </form>
</div>