<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Alert</strong>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Angular Alert is prepared for any length of text, as well as an optional close button.
          For a styling, use one of the <strong>required</strong> contextual <code>color</code>
          props (e.g., <code>primary</code>). For inline dismissal, use the
          <a href="https://coreui.io/angular/docs/4.0/components/alert#dismissing">
            dismissing prop
          </a>
          .
        </p>
        <app-docs-example href="components/alert">
          <c-alert color="primary">A simple primary alert—check it out!</c-alert>
          <c-alert color="secondary">A simple secondary alert—check it out!</c-alert>
          <c-alert color="success">A simple success alert—check it out!</c-alert>
          <c-alert color="danger">A simple danger alert—check it out!</c-alert>
          <c-alert color="warning">A simple warning alert—check it out!</c-alert>
          <c-alert color="info">A simple info alert—check it out!</c-alert>
          <c-alert color="light">A simple light alert—check it out!</c-alert>
          <c-alert color="dark">A simple dark alert—check it out!</c-alert>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Alert</strong> <small>solid variant</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="components/alert">
          <c-alert color="primary" variant="solid">A solid primary alert—check it out!</c-alert>
          <c-alert color="secondary" variant="solid">A solid secondary alert—check it out!</c-alert>
          <c-alert color="success" variant="solid">A solid success alert—check it out!</c-alert>
          <c-alert color="danger" variant="solid">A solid danger alert—check it out!</c-alert>
          <c-alert color="warning" variant="solid">A solid warning alert—check it out!</c-alert>
          <c-alert color="info" variant="solid">A solid info alert—check it out!</c-alert>
          <c-alert color="light" variant="solid">A solid light alert—check it out!</c-alert>
          <c-alert color="dark" variant="solid">A solid dark alert—check it out!</c-alert>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Alert</strong> <small>Link color</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use the <code>cAlertLink</code> directive to immediately give matching colored
          links inside any alert.
        </p>
        <app-docs-example href="components/alert#link-color">
          <c-alert color="primary">
            A simple primary alert with <a cAlertLink [routerLink]="[]">an example link</a>. Give
            it a click if you like.
          </c-alert>
          <c-alert color="secondary">
            A simple secondary alert with <a cAlertLink [routerLink]="[]">an example link</a>.
            Give it a click if you like.
          </c-alert>
          <c-alert color="success">
            A simple success alert with <a cAlertLink [routerLink]="[]">an example link</a>. Give
            it a click if you like.
          </c-alert>
          <c-alert color="danger">
            A simple danger alert with <a cAlertLink [routerLink]="[]">an example link</a>. Give
            it a click if you like.
          </c-alert>
          <c-alert color="warning">
            A simple warning alert with <a cAlertLink [routerLink]="[]">an example link</a>. Give
            it a click if you like.
          </c-alert>
          <c-alert color="info">
            A simple info alert with <a cAlertLink [routerLink]="[]">an example link</a>. Give it
            a click if you like.
          </c-alert>
          <c-alert color="light">
            A simple light alert with <a cAlertLink [routerLink]="[]">an example link</a>. Give it
            a click if you like.
          </c-alert>
          <c-alert color="dark">
            A simple dark alert with <a cAlertLink [routerLink]="[]">an example link</a>. Give it
            a click if you like.
          </c-alert>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Alert</strong> <small>Additional content</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Alert can also incorporate supplementary components &amp; elements like heading,
          paragraph, and divider.
        </p>
        <app-docs-example href="components/alert#additional-content">
          <c-alert color="success">
            <h4 cAlertHeading>Well done!</h4>
            <p>
              Aww yeah, you successfully read this important alert message. This example text is
              going to run a bit longer so that you can see how spacing within an alert works
              with this kind of content.
            </p>
            <hr />
            <p class="mb-0">
              Whenever you need to, be sure to use margin utilities to keep things nice and
              tidy.
            </p>
          </c-alert>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Alert</strong> <small>Icons</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="components/alert">
          <c-alert color="dark" class="d-flex align-items-center">
            <svg cIcon name="cilCheck" class="flex-shrink-0 me-2" size="xl"></svg>
            <div>An example alert with an icon</div>
          </c-alert>
          <c-alert color="secondary" class="d-flex align-items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                 class="bi bi-exclamation-triangle-fill flex-shrink-0 me-2" viewBox="0 0 16 16" role="img"
                 aria-label="Warning:">
              <path
                d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z" />
            </svg>
            <div>An example alert with an icon</div>
          </c-alert>

          <hr>

          <svg xmlns="http://www.w3.org/2000/svg" class="d-none">
            <symbol id="check-circle-fill" fill="currentColor" viewBox="0 0 16 16">
              <path
                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
            </symbol>
            <symbol id="info-fill" fill="currentColor" viewBox="0 0 16 16">
              <path
                d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
            </symbol>
            <symbol id="exclamation-triangle-fill" fill="currentColor" viewBox="0 0 16 16">
              <path
                d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z" />
            </symbol>
          </svg>

          <c-alert color="primary" class="d-flex align-items-center">
            <svg class="bi flex-shrink-0 me-2" width="24" height="24" role="img" aria-label="Info:">
              <use xlink:href="#info-fill" />
            </svg>
            <div>An example primary alert with an icon</div>
          </c-alert>
          <c-alert color="success" class="d-flex align-items-center">
            <svg class="bi flex-shrink-0 me-2" width="24" height="24" role="img" aria-label="Info:">
              <use xlink:href="#check-circle-fill" />
            </svg>
            <div>An example success alert with an icon</div>
          </c-alert>
          <c-alert color="warning" class="d-flex align-items-center">
            <svg class="bi flex-shrink-0 me-2" width="24" height="24" role="img" aria-label="Info:">
              <use xlink:href="#exclamation-triangle-fill" />
            </svg>
            <div>An example warning alert with an icon</div>
          </c-alert>
          <c-alert color="danger" class="d-flex align-items-center">
            <svg class="bi flex-shrink-0 me-2" width="24" height="24" role="img" aria-label="Info:">
              <use xlink:href="#exclamation-triangle-fill" />
            </svg>
            <div>An example danger alert with an icon</div>
          </c-alert>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Alert</strong> <small>Dismissing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Alerts can also be easily dismissed. Just add the <code>dismissible</code> prop.
        </p>
        <app-docs-example href="components/alert#dismissing">
          @if (visible[0]) {
            <c-alert [(visible)]="visible[0]" dismissible color="warning" fade>
              <strong>Go right ahead</strong> and click that dismiss over there on the right.
            </c-alert>
          }
          <c-alert #alertWithButtonCloseTemplate="cAlert"
                   [dismissible]="dismissible"
                   [visible]="visible[1]"
                   (visibleChange)="onAlertVisibleChange($event)"
                   color="dark"
                   fade
                   variant="solid">
            @if (alertWithButtonCloseTemplate.dismissible) {
              <ng-template cTemplateId="alertButtonCloseTemplate">
                <button (click)="visible[1]=false" cButtonClose white></button>
              </ng-template>
            }
            <strong>Go right ahead</strong> and click that dismiss over there on the right.
          </c-alert>
          <hr>
          <button (click)="onToggleDismiss()" cButton class="'me-1'" color="primary">Toggle</button>
          <button (click)="onResetDismiss()" cButton color="secondary">Reset</button>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
