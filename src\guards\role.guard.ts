import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { RbacService } from '../services/rbac.service';

export const roleGuard: CanActivateFn = (route, state) => {
  const rbacService =inject(RbacService);
  const router = inject(Router);
  const requiredRoles = route.data['roles'] as string[] || [];

  if (requiredRoles.length === 0 || requiredRoles.some(role => rbacService.canAccess('access', role))) {
    return true;
  }
  router.navigate(['auth/unauthorized']);
  return false;
};
