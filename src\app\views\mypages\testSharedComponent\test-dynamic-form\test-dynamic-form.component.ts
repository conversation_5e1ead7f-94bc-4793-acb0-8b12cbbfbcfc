import { Component } from '@angular/core';
import { FormField, FormAction, FormConfig } from '../../../../../shared/models/form.models';
import { SmartContainerComponent } from "../../../../../shared/components/smart-container/smart-container.component";
import { DynamicFormComponent } from '../../../../../shared/components/dynamic-form/dynamic-form.component';

@Component({
  standalone: true,
  selector: 'app-test-dynamic-form',
  templateUrl: 'test-dynamic-form.component.html',
  styleUrl: './test-dynamic-form.component.scss',
  imports: [SmartContainerComponent,DynamicFormComponent]
})
export class TestDynamicFormComponent {
  countries = [
    { id: 1, name: 'United States', code: 'US' },
    { id: 2, name: 'Canada', code: 'CA' },
    { id: 3, name: 'United Kingdom', code: 'UK' },
    { id: 4, name: 'Australia', code: 'AU' }
  ];

  interests = [
    { id: 1, title: 'Sports', category: 'hobby' },
    { id: 2, title: 'Music', category: 'art' },
    { id: 3, title: 'Reading', category: 'education' },
    { id: 4, title: 'Travel', category: 'hobby' }
  ];

  formConfig: FormConfig = {
    title: 'User Profile',
    layout: 'vertical',
    fields: [
      {
        type: 'text',
        name: 'name',
        label: 'Full Name',
        placeholder: 'Enter your full name',
        required: true
      },
      {
        type: 'email',
        name: 'email',
        label: 'Email Address',
        placeholder: 'Enter your email',
        required: true
      },
      {
        type: 'select',
        name: 'country',
        label: 'Country',
        required: true,
        options: this.countries,
        optionValue: 'code',  // Use 'code' property as value
        optionLabel: 'name'   // Use 'name' property as label
      },
      {
        type: 'multiselect',
        name: 'interests',
        label: 'Interests',
        options: this.interests,
        optionValue: 'id',    // Use 'id' property as value
        optionLabel: 'title'  // Use 'title' property as label
      },
      {
        type: 'image',
        name: 'avatar',
        label: 'Profile Picture',
        multipleImage: false, // Allow only single image
        accept: 'image/png, image/jpeg' // Only accept PNG and JPEG
      },
      {
        type: 'image',
        name: 'gallery',
        label: 'Photo Gallery',
        multipleImage: true,  // Allow multiple images
        accept: 'image/*'     // Accept any image type
      },
      {
        type: 'textarea',
        name: 'bio',
        label: 'Biography',
        placeholder: 'Tell us about yourself'
      }
    ],
    actions: [
      {
        label: 'Cancel',
        color: 'secondary',
        icon: 'cil-x',
        onClick: () => this.onCancel()
      },
      {
        label: 'Save',
        color: 'primary',
        icon: 'cil-save',
        type: 'submit',
        onClick: (formData) => this.onFormSubmit(formData),
      }
    ]
  };

  onFormSubmit(submitData: any) {
    console.log('Form submitted:', submitData);
    console.log('Processed Data:', submitData.processedData);
    console.log('Raw Form Value:', submitData.rawFormValue);
    console.log('FormData:', submitData.formData);

    // Process form data here
    // You can access the data in different formats:
    // - submitData.formData: FormData object for file uploads
    // - submitData.processedData: Clean object with processed values
    // - submitData.rawFormValue: Raw form values from Angular FormGroup

    alert(`Form submitted successfully! Check console for details.`);
  }

  onCancel() {
    console.log('Form cancelled');
    // Handle cancel action
  
}
}