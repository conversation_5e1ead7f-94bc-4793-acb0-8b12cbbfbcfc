import { FrenchDatePipe } from './french-date.pipe';

describe('FrenchDatePipe', () => {
  let pipe: FrenchDatePipe;

  beforeEach(() => {
    pipe = new FrenchDatePipe();
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should transform ISO date string to French format', () => {
    expect(pipe.transform('2023-02-20')).toBe('20 février 2023');
    expect(pipe.transform('2023-01-15')).toBe('15 janvier 2023');
    expect(pipe.transform('2023-12-25')).toBe('25 décembre 2023');
  });

  it('should transform Date object to French format', () => {
    const date = new Date(2023, 1, 20); // February 20, 2023
    expect(pipe.transform(date)).toBe('20 février 2023');
  });

  it('should handle all French months correctly', () => {
    expect(pipe.transform('2023-01-01')).toBe('1 janvier 2023');
    expect(pipe.transform('2023-02-01')).toBe('1 février 2023');
    expect(pipe.transform('2023-03-01')).toBe('1 mars 2023');
    expect(pipe.transform('2023-04-01')).toBe('1 avril 2023');
    expect(pipe.transform('2023-05-01')).toBe('1 mai 2023');
    expect(pipe.transform('2023-06-01')).toBe('1 juin 2023');
    expect(pipe.transform('2023-07-01')).toBe('1 juillet 2023');
    expect(pipe.transform('2023-08-01')).toBe('1 août 2023');
    expect(pipe.transform('2023-09-01')).toBe('1 septembre 2023');
    expect(pipe.transform('2023-10-01')).toBe('1 octobre 2023');
    expect(pipe.transform('2023-11-01')).toBe('1 novembre 2023');
    expect(pipe.transform('2023-12-01')).toBe('1 décembre 2023');
  });

  it('should return empty string for null or undefined', () => {
    expect(pipe.transform(null)).toBe('');
    expect(pipe.transform(undefined)).toBe('');
  });

  it('should return empty string for invalid dates', () => {
    expect(pipe.transform('invalid-date')).toBe('');
    expect(pipe.transform('not-a-date')).toBe('');
    expect(pipe.transform('')).toBe('');
  });

  it('should handle edge cases', () => {
    expect(pipe.transform('2023-02-29')).toBe(''); // Invalid date (not a leap year)
    expect(pipe.transform('2024-02-29')).toBe('29 février 2024'); // Valid leap year date
  });
});
