<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Placeholder</strong>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          In the example below, we take a typical card component and recreate it with
          placeholders applied to create a &#34;loading card&#34;. Size and proportions are the
          same between the two.
        </p>
        <app-docs-example href="components/placeholder">
          <div class="d-flex justify-content-around p-3">
            <c-card style="width: 18rem;">
              <img cCardImg="top" loading="lazy" src="./assets/images/angular.jpg" />
              <c-card-body>
                <h5 cCardTitle>Card title</h5>
                <p cCardText>
                  Some quick example text to build on the card title and make up the bulk of the
                  card&#39;s content.
                </p>
                <a cButton cCol="7" routerLink="./">Go somewhere</a>
              </c-card-body>
            </c-card>
            <c-card style="width: 18rem;">
              <svg aria-label="Placeholder"
                   cCardImg="top"
                   focusable="false"
                   height="162"
                   preserveAspectRatio="xMidYMid slice"
                   role="img"
                   width="100%"
                   xmlns="http://www.w3.org/2000/svg"
              >
                <title>Placeholder</title>
                <rect fill="#868e96" height="100%" width="100%"></rect>
              </svg>
              <c-card-body>
                <h5 cCardTitle cPlaceholderAnimation="glow">
                  <span cCol="6" cPlaceholder></span>
                </h5>
                <p cCardText cPlaceholderAnimation="glow">
                  <span cCol xs="7" cPlaceholder class="me-1"></span>
                  <span cCol="4" cPlaceholder class="me-1"></span>
                  <span cCol="4" cPlaceholder class="me-1"></span>
                  <span cCol="6" cPlaceholder class="me-1"></span>
                  <span cCol="8" cPlaceholder class="me-1"></span>
                </p>
                <p cPlaceholderAnimation="glow">
                  <a cButton cCol="7" cPlaceholder color="primary" disabled routerLink="./"></a>
                </p>
              </c-card-body>
            </c-card>
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Placeholder</strong>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Create placeholders with the <code>cPlaceholder</code> directive and a grid
          column cCol directive (e.g., <code>cCol="6"</code>) to set the <code>width</code>. They can
          replace the text inside an element or be added as a modifier to an existing
          component.
        </p>
        <app-docs-example href="components/placeholder">
          <p aria-hidden="true">
            <span cCol="6" cPlaceholder></span>
          </p>
          <button cButton cCol="4" cPlaceholder disabled></button>
        </app-docs-example>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Placeholder</strong> <small> Width</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          You can change the <code>width</code> through grid column classes, width utilities, or
          inline styles.
        </p>
        <app-docs-example href="components/placeholder#width">
          <span cCol="6" cPlaceholder></span>
          <span cPlaceholder class="w-75"></span>
          <span cPlaceholder style="width: 30%;"></span>
        </app-docs-example>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Placeholder</strong> <small> Color</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          By default, the <code>cPlaceholder</code> uses <code>currentColor</code>. This
          can be overridden with a custom color or utility class.
        </p>
        <app-docs-example href="components/placeholder#color">
          <span cCol="12" cPlaceholder></span>

          <span cBgColor="primary" cCol="12" cPlaceholder></span>
          <span cBgColor="secondary" cCol="12" cPlaceholder></span>
          <span cBgColor="success" cCol="12" cPlaceholder></span>
          <span cBgColor="danger" cCol="12" cPlaceholder></span>
          <span cBgColor="warning" cCol="12" cPlaceholder></span>
          <span cBgColor="info" cCol="12" cPlaceholder></span>
          <span cBgColor="light" cCol="12" cPlaceholder></span>
          <span cBgColor="dark" cCol="12" cPlaceholder></span>
        </app-docs-example>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Placeholder</strong> <small> Sizing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          The size of <code>cPlaceholder</code>s are based on the typographic style of
          the parent element. Customize them with <code>size</code> prop: <code>lg</code>, <code>sm</code>, or
          <code>xs</code>.
        </p>
        <app-docs-example href="components/placeholder#sizing">
          <span cCol="12" cPlaceholder cPlaceholderSize="lg"></span>
          <span cCol="12" cPlaceholder></span>
          <span cCol="12" cPlaceholder cPlaceholderSize="sm"></span>
          <span cCol="12" cPlaceholder cPlaceholderSize="xs"></span>
        </app-docs-example>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Placeholder</strong> <small> Animation</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Animate placeholders with <code>cPlaceholderAnimation=&#34;glow&#34;</code> or
          <code>cPlaceholderAnimation=&#34;wave&#34;</code> to better convey the perception of something
          being <em>actively</em> loaded.
        </p>
        <app-docs-example href="components/placeholder#animation">
          <p cPlaceholderAnimation="glow">
            <span cCol="12" cPlaceholder></span>
          </p>

          <p cPlaceholderAnimation="wave">
            <span cCol="12" cPlaceholder></span>
          </p>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
