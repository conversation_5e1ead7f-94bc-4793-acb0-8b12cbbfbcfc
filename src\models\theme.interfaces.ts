/**
 * Theme configuration interfaces for the customizer component
 * Based on zen-logistic implementation with Angular best practices
 */

export interface TemplateConfig {
  layout: {
    variant: string; // 'Light', 'Dark'
    dir: string; // 'ltr', 'rtl'
    customizer: {
      hidden: boolean;
    };
    sidebar: {
      collapsed: boolean;
      size: SidebarSize; // 'sidebar-lg', 'sidebar-md', 'sidebar-sm'
      backgroundColor: string;
      backgroundImage: boolean;
      backgroundImageURL: string;
    };
  };
}

export interface CustomizerOptions {
  direction: string;
  bgColor: string;
  bgImage: string;
  bgImageDisplay: boolean;
  compactMenu: boolean;
  sidebarSize: SidebarSize;
}

export type CustomizerChangeType =
  | 'toggle'
  | 'direction'
  | 'bgColor'
  | 'bgImage'
  | 'bgImageDisplay'
  | 'compactMenu'
  | 'sidebarSize';

export interface ThemeColorData {
  color: string;
}

export interface ThemeUpdateResponse {
  id: string | number;
  color: string;
}

/**
 * Available sidebar background colors
 */
export const SIDEBAR_COLORS = {
  BLACK: 'black',
  POMEGRANATE: 'pomegranate',
  KING_YNA: 'king-yna',
  IBIZA_SUNSET: 'ibiza-sunset',
  FLICKR: 'flickr',
  PURPLE_BLISS: 'purple-bliss',
  MAN_OF_STEEL: 'man-of-steel',
  PURPLE_LOVE: 'purple-love',
  PRIMARY: 'primary',
  WHITE: 'white'
} as const;



/**
 * Available sidebar sizes
 */
export const SIDEBAR_SIZES = {
  LARGE: 'sidebar-lg',
  MEDIUM: 'sidebar-md',
  SMALL: 'sidebar-sm'
} as const;



export type SidebarColor = typeof SIDEBAR_COLORS[keyof typeof SIDEBAR_COLORS];
export type SidebarSize = typeof SIDEBAR_SIZES[keyof typeof SIDEBAR_SIZES];
