// Place for temp fixes

// Fix for sidebar navigation layout issues
// Ensure proper z-index hierarchy and positioning

// Fix sidebar z-index to ensure it stays below header but above main content
.sidebar {
  z-index: 1020 !important; // Below header (1030) but above main content
}

// Ensure header has proper z-index and responsive positioning
.header,
c-header {
  z-index: 1030 !important; // Above sidebar
  position: sticky !important;
  top: 0;
  width: 100%;
  // Use CoreUI's sidebar occupy variables for proper responsive behavior
  margin-left: var(--cui-sidebar-occupy-start, 0);
  margin-right: var(--cui-sidebar-occupy-end, 0);
  transition: margin 0.15s ease-in-out;
}

// Restore proper wrapper behavior with CoreUI responsive system
.wrapper {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  // Let CoreUI handle the padding based on sidebar state
  padding-inline: var(--cui-sidebar-occupy-start, 0) var(--cui-sidebar-occupy-end, 0);
  transition: padding 0.15s ease-in-out;
}

// Fix for sidebar navigation items not displaying properly
.sidebar-nav {
  position: relative;
  z-index: 1;
  width: 100%;

  // Ensure navigation items are visible
  .nav-item,
  .nav-link {
    display: block !important;
    visibility: visible !important;
  }

  // Fix for navigation items appearing above header
  .nav-dropdown {
    position: relative;
    z-index: 1;
  }
}

// Ensure ng-scrollbar doesn't interfere with navigation display
ng-scrollbar {
  .ng-scroll-content {
    display: block !important;
    width: 100%;
    height: 100%;
  }

  .ng-scroll-viewport {
    width: 100% !important;
    height: 100% !important;
  }
}

// Fix for sidebar content positioning
.sidebar-content {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
}

// Ensure main wrapper doesn't overlap sidebar
.wrapper {
  position: relative;
  z-index: 1;
  // Remove any conflicting padding rules here - handled in media query below
}

// Fix for compact sidebar navigation
.sidebar.sidebar-narrow {
  .sidebar-nav {
    .nav-item {
      display: block !important;
    }
  }
}

// Additional fixes for CoreUI sidebar navigation
c-sidebar-nav {
  display: block !important;
  width: 100%;

  .nav {
    display: block !important;
    width: 100%;
  }

  .nav-item {
    display: block !important;
    width: 100%;
  }
}

// Ensure sidebar brand and header are properly positioned
.sidebar-header {
  position: relative;
  z-index: 2;
}

.sidebar-brand {
  position: relative;
  z-index: 2;
}

// Responsive behavior for mobile vs desktop
@media (max-width: 991.98px) {
  // On mobile, sidebar becomes overlay, so no margin needed
  .header,
  c-header {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .wrapper {
    padding-inline: 0 !important;
  }
}

@media (min-width: 992px) {
  // On desktop, manually handle sidebar spacing based on CoreUI classes

  // Default state: sidebar is visible and expanded (256px width)
  .sidebar.sidebar-fixed[visible] ~ .wrapper,
  .sidebar.sidebar-fixed:not([hidden]) ~ .wrapper {
    padding-left: 256px;
    transition: padding-left 0.15s ease-in-out;
  }

  // When sidebar is narrow/collapsed (56px width)
  .sidebar.sidebar-fixed.sidebar-narrow ~ .wrapper,
  .sidebar.sidebar-fixed.sidebar-unfoldable.sidebar-narrow ~ .wrapper {
    padding-left: 56px !important;
  }

  // When sidebar is completely hidden
  .sidebar.sidebar-fixed[hidden] ~ .wrapper,
  .sidebar.sidebar-fixed.d-none ~ .wrapper {
    padding-left: 0;
  }

  // Ensure header doesn't have additional margin since wrapper handles spacing
  .header,
  c-header {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  // Fallback: if no specific sidebar state is detected, assume expanded
  .wrapper {
    padding-left: 256px;
    transition: padding-left 0.15s ease-in-out;
  }
}

// Ensure main content area respects sidebar spacing
.body {
  flex: 1;
  width: 100%;

  .container-lg {
    max-width: none;
    width: 100%;
  }
}
