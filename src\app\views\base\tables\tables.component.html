<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Basic example</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Using the most basic table CoreUI, here&#39;s how <code>cTable</code>-based
          tables look in CoreUI.
        </p>
        <app-docs-example href="components/table">
          <table cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2"><PERSON></td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Variants</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use contextual classes to color tables, table rows or individual cells.
        </p>
        <app-docs-example href="components/table#variants">
          <table cTable>
            <thead>
            <tr>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">Default</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            <tr cTableColor="primary">
              <th scope="row">Primary</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            <tr cTableColor="secondary">
              <th scope="row">Secondary</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            <tr cTableColor="success">
              <th scope="row">Success</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            <tr cTableColor="danger">
              <th scope="row">Danger</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            <tr cTableColor="warning">
              <th scope="row">Warning</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            <tr cTableColor="info">
              <th scope="row">Info</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            <tr cTableColor="light">
              <th scope="row">Light</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            <tr cTableColor="dark">
              <th scope="row">Dark</th>
              <td>Cell</td>
              <td>Cell</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Striped rows</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use <code>striped</code> property to add zebra-striping to any table row within the <code>&lt;tbody&gt;</code>.
        </p>
        <app-docs-example href="components/table#striped-rows">
          <table [striped]="true" cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <p class="text-body-secondary small">
          These classes can also be added to table variants:
        </p>
        <app-docs-example href="components/table#striped-rows">
          <table [striped]="true" cTable color="dark">
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <app-docs-example href="components/table#striped-rows">
          <table [striped]="true" cTable color="success">
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Striped columns</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use <code>stripedColumn</code> property to add zebra-striping to any table column.
        </p>
        <app-docs-example href="components/table#striped-columns">
          <table stripedColumns cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Hoverable rows</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use <code>[hover]="true"</code> property to enable a hover state on table rows within a
          <code>&lt;tbody&gt;</code>.
        </p>
        <app-docs-example href="components/table#hoverable-rows">
          <table [hover]="true" cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <app-docs-example href="components/table#hoverable-rows">
          <table [hover]="true" cTable color="dark">
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <app-docs-example href="components/table#hoverable-rows">
          <table [hover]="true" [striped]="true" cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Active tables</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="components/table#active-tables">
          <table cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr cTableActive>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td cTableActive colSpan="2">
                Larry the Bird
              </td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <app-docs-example href="components/table#active-tables">
          <table cTable color="dark">
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr cTableActive>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td cTableActive colSpan="2">
                Larry the Bird
              </td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Bordered tables</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>bordered</code> property for borders on all sides of the table and cells.
        </p>
        <app-docs-example href="components/table#bordered-tables">
          <table [bordered]="true" cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <p class="text-body-secondary small">
          <a href="https://coreui.io/docs/4.0/utilities/borders#border-color">
            Border color utilities
          </a> can be added to change colors:
        </p>
        <app-docs-example href="components/table#bordered-tables">
          <table [bordered]="true" [cBorder]="1" borderColor="primary" cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Tables without borders</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>borderless</code> property for a table without borders.
        </p>
        <app-docs-example href="components/table#tables-without-borders">
          <table [borderless]="true" cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <app-docs-example href="components/table#tables-without-borders">
          <table borderless cTable color="dark">
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Small tables</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>small</code> property to make any <code>cTable</code> more compact
          by cutting all cell <code>padding</code> in half.
        </p>
        <app-docs-example href="components/table#small-tables">
          <table [small]="true" cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Vertical alignment</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Table cells of <code>&lt;thead&gt;</code> are always vertical aligned to the
          bottom. Table cells in <code>&lt;tbody&gt;</code> inherit their alignment from
          <code>cTable</code> and are aligned to the the top by default. Use the align
          property to re-align where needed.
        </p>
        <app-docs-example href="components/table#vertical-alignment">
          <table [responsive]="true" cAlign="middle" cTable>
            <thead>
            <tr>
              <th class="w-25" scope="col">
                Heading 1
              </th>
              <th class="w-25" scope="col">
                Heading 2
              </th>
              <th class="w-25" scope="col">
                Heading 3
              </th>
              <th class="w-25" scope="col">
                Heading 4
              </th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td>
                This cell inherits <code>vertical-align: middle;</code> from the table
              </td>
              <td>
                This cell inherits <code>vertical-align: middle;</code> from the table
              </td>
              <td>
                This cell inherits <code>vertical-align: middle;</code> from the table
              </td>
              <td>
                This here is some placeholder text, intended to take up quite a bit of
                vertical space, to demonstrate how the vertical alignment works in the
                preceding cells.
              </td>
            </tr>
            <tr cAlign="bottom">
              <td>
                This cell inherits <code>vertical-align: bottom;</code> from the table row
              </td>
              <td>
                This cell inherits <code>vertical-align: bottom;</code> from the table row
              </td>
              <td>
                This cell inherits <code>vertical-align: bottom;</code> from the table row
              </td>
              <td>
                This here is some placeholder text, intended to take up quite a bit of
                vertical space, to demonstrate how the vertical alignment works in the
                preceding cells.
              </td>
            </tr>
            <tr>
              <td>
                This cell inherits <code>vertical-align: middle;</code> from the table
              </td>
              <td>
                This cell inherits <code>vertical-align: middle;</code> from the table
              </td>
              <td cAlign="top">This cell is aligned to the top.</td>
              <td>
                This here is some placeholder text, intended to take up quite a bit of
                vertical space, to demonsCTableRowate how the vertical alignment works in the
                preceding cells.
              </td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Nesting</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Border styles, active styles, and table variants are not inherited by nested tables.
        </p>
        <app-docs-example href="components/table#nesting">
          <table [striped]="true" cTable>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th colSpan="4">
                <table cTable>
                  <thead>
                  <tr>
                    <th scope="col">Header</th>
                    <th scope="col">Header</th>
                    <th scope="col">Header</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr>
                    <th scope="row">A</th>
                    <td>First</td>
                    <td>Last</td>
                  </tr>
                  <tr>
                    <th scope="row">B</th>
                    <td>First</td>
                    <td>Last</td>
                  </tr>
                  <tr>
                    <th scope="row">C</th>
                    <td>First</td>
                    <td>Last</td>
                  </tr>
                  </tbody>
                </table>
              </th>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Table head</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Similar to tables and dark tables, use the modifier prop
          <code>color=&#34;light&#34;</code> or <code>color=&#34;dark&#34;</code> to make
          <code>&lt;thead&gt;</code>s appear light or dark gray.
        </p>
        <app-docs-example href="components/table#table-head">
          <table cTable>
            <thead cTableColor="light">
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td>Larry</td>
              <td>the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <app-docs-example href="components/table#table-head">
          <table cTable>
            <thead cTableColor="dark">
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Table foot</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="components/table#table-foot">
          <table cTable>
            <thead cTableColor="light">
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td colSpan="2">Larry the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
            <tfoot>
            <tr>
              <td>Footer</td>
              <td>Footer</td>
              <td>Footer</td>
              <td>Footer</td>
            </tr>
            </tfoot>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Table</strong> <small>Captions</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          A <code>&lt;caption&gt;</code> functions like a heading for a table. It helps
          users with screen readers to find a table and understand what it's about and
          decide if they want to read it.
        </p>
        <app-docs-example href="components/table#captions">
          <table cTable>
            <caption>List of users</caption>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td>Larry</td>
              <td>the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
        <p class="text-body-secondary small">
          You can also put the <code>&lt;caption&gt;</code> on the top of the table with
          <code>caption=&#34;top&#34;</code>.
        </p>
        <app-docs-example href="components/table#captions">
          <table cTable caption="top">
            <caption>List of users</caption>
            <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Class</th>
              <th scope="col">Heading</th>
              <th scope="col">Heading</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <th scope="row">1</th>
              <td>Mark</td>
              <td>Otto</td>
              <td>&#64;mdo</td>
            </tr>
            <tr>
              <th scope="row">2</th>
              <td>Jacob</td>
              <td>Thornton</td>
              <td>&#64;fat</td>
            </tr>
            <tr>
              <th scope="row">3</th>
              <td>Larry</td>
              <td>the Bird</td>
              <td>&#64;twitter</td>
            </tr>
            </tbody>
          </table>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
