export interface StatusConfig {
  value: any;
  displayText: string;
  badgeColor: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  icon?: string;
  customClasses?: string;
}

export interface TableColumn {
  name: string;
  displayName: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  dataType?: 'text' | 'number' | 'date' | 'boolean' | 'status';
  statusConfig?: StatusConfig[];
}

export interface TableAction {
  name: string;
  label?: string;
  icon?: string;
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  condition?: (row: any) => boolean;
  hidden?: boolean;
  callback?: (row: any) => void;
  cssClasses?: string;
  tooltip?: string;
  disabled?: (row: any) => boolean;
}

export interface ActionButton {
  label?: string;
  icon?: string;
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  cssClasses?: string;
  callback: (row: any) => void;
  tooltip?: string;
  condition?: (row: any) => boolean;
  disabled?: (row: any) => boolean;
}

export interface TableConfig {
  pageSizeOptions?: number[];
  pageSize?: number;
  selectable?: boolean;
  multiSelect?: boolean;
  emptyMessage?: string;
  striped?: boolean;
  hover?: boolean;
  bordered?: boolean;
  small?: boolean;
  showFilter?: boolean;
}