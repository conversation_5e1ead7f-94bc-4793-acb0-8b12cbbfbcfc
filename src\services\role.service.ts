import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';


@Injectable({
  providedIn: 'root'
})
export class RoleService {

  constructor(private authService: AuthService) { }

  getRole() {
    const user = this.authService.getUser();
    return user ? user.role : null;
  }
  hasRole(role: string) {
    return this.getRole()=== role;
  }
  hasAnyRole(roles: string[]): boolean {
    return roles.some(role => this.hasRole(role));
  }
}
