import { Routes } from '@angular/router';
import { authGuard } from '../../../guards/auth.guard';
import { roleGuard } from '../../../guards/role.guard';

export const routes: Routes = [
  {
    path: '',
    canActivate: [authGuard],
    data: {
      title: 'My Pages'
    },
    children: [
        {
            path: 'commandes',
            loadChildren: () => import('./commandes/routes').then((m) => m.routes),
            canActivate: [authGuard, roleGuard],
            data: {
              title: 'Commandes',
              roles: ['Client', 'Chef Departement', 'GS', 'GE', 'Depot', 'SuperAdmin', 'Administrateur']
            }
        },
        {
            path: 'inspection',
            loadChildren: () => import('./inspection/routes').then((m) => m.routes),
            canActivate: [authGuard, roleGuard],
            data: {
              title: 'Inspection',
              roles: ['Inspection', 'SuperAdmin', 'Administrateur']
            }
        }
    ]
  }
];
