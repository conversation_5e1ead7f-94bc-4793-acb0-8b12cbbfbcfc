# Authentication and Role Guards Documentation

This document explains how to use the authentication and role-based access control (RBAC) guards in the CoreUI Angular application.

## Overview

The application provides two main guards:
- **AuthGuard**: Ensures users are authenticated before accessing protected routes
- **RoleGuard**: Ensures authenticated users have the required roles to access specific routes

## Guards

### AuthGuard (`auth.guard.ts`)

The AuthGuard protects routes by checking if the user is authenticated and has a valid session.

**Features:**
- Session validation with expiration checking
- Automatic session cleanup for invalid sessions
- Redirect URL storage for post-login navigation
- Activity tracking updates
- Comprehensive error handling

**Usage:**
```typescript
import { authGuard } from './guards/auth.guard';

const routes: Routes = [
  {
    path: 'protected',
    component: ProtectedComponent,
    canActivate: [authGuard]
  }
];
```

### RoleGuard (`role.guard.ts`)

The RoleGuard ensures users have the required roles to access specific routes.

**Features:**
- Multiple role checking (user needs ANY of the specified roles)
- Support for both `roles` and `expectedRole` data properties
- Integration with AuthService for user data
- Comprehensive logging for debugging
- Automatic redirect to unauthorized page

**Usage:**
```typescript
import { roleGuard } from './guards/role.guard';

const routes: Routes = [
  {
    path: 'admin',
    component: AdminComponent,
    canActivate: [authGuard, roleGuard],
    data: {
      roles: ['SuperAdmin', 'Administrateur']
    }
  }
];
```

## Available Roles

The system supports the following roles:
- `SuperAdmin` - Full system access
- `Administrateur` - Administrative access
- `Client` - Client-specific access
- `Chef Departement` - Department head access
- `GS` - Store manager access
- `GE` - Store employee access
- `Depot` - Warehouse access
- `Inspection` - Quality inspection access
- `FACTURATION` - Billing/invoicing access
- `MAGASIN` - Store/inventory access
- `Conducteur` - Driver access

## Route Configuration Examples

### Basic Authentication Protection
```typescript
{
  path: 'dashboard',
  component: DashboardComponent,
  canActivate: [authGuard]
}
```

### Role-Based Protection
```typescript
{
  path: 'admin-panel',
  component: AdminPanelComponent,
  canActivate: [authGuard, roleGuard],
  data: {
    roles: ['SuperAdmin', 'Administrateur']
  }
}
```

### Child Route Protection
```typescript
{
  path: 'management',
  canActivate: [authGuard],
  children: [
    {
      path: 'users',
      component: UserManagementComponent,
      canActivate: [roleGuard],
      data: {
        roles: ['SuperAdmin', 'Administrateur']
      }
    },
    {
      path: 'orders',
      component: OrderManagementComponent,
      canActivate: [roleGuard],
      data: {
        roles: ['Client', 'Chef Departement', 'GS']
      }
    }
  ]
}
```

### Multiple Guards with Complex Roles
```typescript
{
  path: 'inspection',
  loadChildren: () => import('./inspection/routes').then(m => m.routes),
  canActivate: [authGuard, roleGuard],
  data: {
    title: 'Quality Inspection',
    roles: ['Inspection', 'SuperAdmin', 'Administrateur']
  }
}
```

## Services

### RoleService (`role.service.ts`)

Provides role management functionality:

**Methods:**
- `getRole()`: Get current user's role synchronously
- `getRole$()`: Get current user's role as Observable
- `hasRole(role)`: Check if user has specific role
- `hasAnyRole(roles)`: Check if user has any of the specified roles
- `hasAllRoles(roles)`: Check if user has all specified roles
- `getAvailableRoles()`: Get list of all available roles
- `isValidRole(role)`: Check if a role exists in the system

### RbacService (`rbac.service.ts`)

Provides role-based access control functionality:

**Methods:**
- `canAccess(permission, role)`: Check access with specific role
- `canAccessWithAnyRole(permission, roles)`: Check access with any role
- `isAuthorized(role)`: Check if user is authenticated and has role
- `getUserPermissions()`: Get user's permissions based on role
- `hasPermission(permission)`: Check if user has specific permission

## Error Handling

### Authentication Failures
- Invalid sessions redirect to `/login`
- Attempted URL is stored for post-login redirect
- Session cleanup is performed automatically

### Authorization Failures
- Insufficient roles redirect to `/unauthorized`
- Detailed logging for debugging
- Graceful error handling with fallback redirects

## Best Practices

1. **Always use AuthGuard first**: Place `authGuard` before `roleGuard` in the `canActivate` array
2. **Specify required roles**: Always define the `roles` array in route data
3. **Use least privilege**: Only grant the minimum roles necessary for each route
4. **Handle unauthorized access**: Ensure you have an `/unauthorized` route
5. **Test role combinations**: Verify that role combinations work as expected

## Debugging

Enable console logging to debug guard behavior:
- AuthGuard logs session validation and authentication status
- RoleGuard logs role checking and access decisions
- Check browser console for detailed guard execution information

## Migration from Zen-Logistic

When migrating routes from zen-logistic, update the guard usage:

**Old (zen-logistic):**
```typescript
canActivate: [AuthGuard], // Class-based guard
data: {
  expectedRole: ['SuperAdmin', 'Administrateur']
}
```

**New (CoreUI):**
```typescript
canActivate: [authGuard, roleGuard], // Functional guards
data: {
  roles: ['SuperAdmin', 'Administrateur'] // Updated property name
}
```

Both `expectedRole` and `roles` are supported for backward compatibility.
