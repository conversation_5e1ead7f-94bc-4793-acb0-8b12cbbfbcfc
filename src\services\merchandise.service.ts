import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class MerchandiseService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Add merchandise type
   * @param merchandiseName - Merchandise name
   * @returns Observable with creation result
   * @originalName addTypeMarchandise
   */
  addMerchandiseType(merchandiseName: string): Observable<any> {
    const data = { nom_marchandise: merchandiseName };
    return this.http.post<any>(this.apiURL + 'marchandise', data, httpOptions);
  }

  /**
   * Get all merchandise types
   * @returns Observable with merchandise types list
   * @originalName getAllMarchandise
   */
  getAllMerchandiseTypes(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'marchandise', httpOptions);
  }

  /**
   * Delete merchandise type
   * @param id - Merchandise type ID
   * @returns Observable with deletion result
   * @originalName deleteMarchandise
   */
  deleteMerchandiseType(id: number): Observable<any> {
    const url = `${this.apiURL}marchandise/${id}`;
    return this.http.delete<any>(url, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Add condition type
   * @param conditionName - Condition name
   * @returns Observable with creation result
   * @originalName addTypeCondition
   */
  addConditionType(conditionName: string): Observable<any> {
    const data = { nom_condition: conditionName };
    return this.http.post<any>(this.apiURL + 'condition', data, httpOptions);
  }

  /**
   * Get all condition types
   * @returns Observable with condition types list
   * @originalName getAllCondition
   */
  getAllConditionTypes(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'condition', httpOptions);
  }

  /**
   * Delete condition type
   * @param id - Condition type ID
   * @returns Observable with deletion result
   */
  deleteConditionType(id: number): Observable<any> {
    const url = `${this.apiURL}condition/${id}`;
    return this.http.delete<any>(url, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all order types
   * @returns Observable with order types list
   * @originalName getAlltypeCmd
   */
  getAllOrderTypes(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'typecmd', httpOptions);
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
