// Authentication request interfaces
export interface LoginRequest {
  email: string;
  mot_de_passe: string;
}

export interface RegisterRequest {
  sexe?: string;
  nom: string;
  prenom: string;
  cin?: string;
  image_cin?: string;
  type_utilisateur: string;
  forme_juridique?: string;
  raison_sociale?: string;
  num_tva?: string;
  email: string;
  mot_de_passe: string;
  mobile?: string;
  adresse?: string;
  ville?: string;
  code_postal?: string;
  pays?: string;
}

// Authentication response interfaces
export interface LoginResponse {
  id: number;
  email: string;
  nom?: string;
  prenom?: string;
  type_utilisateur: string;
  statut: string;
  role?: string;
  color?: string; // Theme color preference
  // Note: Backend doesn't use JWT tokens based on analysis
  // Authentication is managed through session storage
}

export interface User {
  id?: number;
  iduser: number;
  email: string;
  nom?: string;
  prenom?: string;
  role: string;
  type_utilisateur?: string;
  statut?: string;
  mobile?: string;
  adresse?: string;
  ville?: string;
  code_postal?: string;
  pays?: string;
  color?: string; // Theme color preference
  // Additional fields from backend customer model
  sexe?: string;
  cin?: string;
  image_cin?: string;
  forme_juridique?: string;
  raison_sociale?: string;
  num_tva?: string;
  nom_utilisateur?: string;
  mot_de_passe?: string;
  cle_activation?: string;
  client_direct?: string;
}

// Error response interfaces
export interface ApiError {
  message: string;
  status?: number;
  error?: string;
}

export interface AuthError extends ApiError {
  type: 'INVALID_CREDENTIALS' | 'NETWORK_ERROR' | 'SERVER_ERROR' | 'VALIDATION_ERROR' | 'UNKNOWN_ERROR' | 'INVALID_RESPONSE';
  details?: string;
}

// Session storage interfaces
export interface SessionData {
  iduser: number;
  email: string;
  userRole: string;
  type_utilisateur?: string;
  nom?: string;
  prenom?: string;
  statut?: string;
  color?: string; // Theme color preference
  loginTime: string;
  expiresAt?: string;
  lastActivity?: string;
}

export interface SessionValidationResult {
  isValid: boolean;
  reason?: 'EXPIRED' | 'MISSING_DATA' | 'INVALID_FORMAT' | 'INACTIVE_TOO_LONG';
  message?: string;
}

export interface SessionConfig {
  sessionTimeoutMinutes: number;
  inactivityTimeoutMinutes: number;
  autoRefresh: boolean;
}

// Form validation interfaces
export interface LoginFormData {
  email: string;
  password: string;
}

export interface LoginFormErrors {
  email?: string;
  password?: string;
  general?: string;
}

// API response wrapper
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  success: boolean;
  error?: ApiError;
}

// Role and permission interfaces
export interface Role {
  id: number;
  name: string;
  permissions?: string[];
}

export interface UserPermissions {
  canRead: boolean;
  canWrite: boolean;
  canDelete: boolean;
  canManageUsers: boolean;
}
