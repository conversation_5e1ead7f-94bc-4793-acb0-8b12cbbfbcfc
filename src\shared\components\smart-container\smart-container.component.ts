import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface SmartContainerConfig {
  title: string;
  subtitle?: string;
  showActions?: boolean;
}

@Component({
  selector: 'app-smart-container',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './smart-container.component.html',
  styleUrls: ['./smart-container.component.scss']
})
export class SmartContainerComponent {
  @Input() title: string = '';
  @Input() subtitle?: string;
  @Input() showActions: boolean = true;
}
