import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class DriverService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Add new driver
   * @param driverData - Driver data
   * @returns Observable with creation result
   * @originalName addConducteur
   */
  addDriver(driverData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'conducteur', driverData, httpOptions);
  }

  /**
   * Get all drivers by transporter
   * @param id - Transporter ID
   * @returns Observable with drivers list
   * @originalName findAllConducteur
   */
  getAllDriversByTransporter(id: string): Observable<any> {
    return this.http.get(this.apiURL + 'conducteurTr/' + id, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all drivers (admin view)
   * @returns Observable with drivers list
   * @originalName findConducteurAdmin
   */
  getAllDriversAdmin(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur', httpOptions);
  }

  /**
   * Block driver
   * @param id - Driver ID
   * @returns Observable with blocking result
   * @originalName conduceurBloqued
   */
  blockDriver(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur/conduceurBloqued/' + id, httpOptions);
  }

  /**
   * Get all drivers
   * @returns Observable with drivers list
   * @originalName findConducteur
   */
  getAllDrivers(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur/', httpOptions);
  }

  /**
   * Find driver by ID
   * @param id - Driver ID
   * @returns Observable with driver data
   * @originalName findConducteurById
   */
  findDriverById(id: string): Observable<any> {
    const url = `${this.apiURL}conducteur/${id}`;
    return this.http.get(url, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
