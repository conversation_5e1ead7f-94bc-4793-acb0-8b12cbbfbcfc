import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { 
  SessionData, 
  SessionValidationResult, 
  SessionConfig, 
  LoginResponse 
} from '../models/auth.interfaces';

@Injectable({
  providedIn: 'root'
})
export class SessionStorageService {
  private readonly SESSION_KEYS = {
    USER_ID: 'iduser',
    EMAIL: 'userEmail',
    ROLE: 'userRole',
    TYPE_UTILISATEUR: 'type_utilisateur',
    NOM: 'nom',
    PRENOM: 'prenom',
    STATUT: 'statut',
    COLOR: 'color',
    LOGIN_TIME: 'loginTime',
    EXPIRES_AT: 'expiresAt',
    LAST_ACTIVITY: 'lastActivity'
  } as const;

  private readonly defaultConfig: SessionConfig = {
    sessionTimeoutMinutes: 480, // 8 hours
    inactivityTimeoutMinutes: 60, // 1 hour
    autoRefresh: true
  };

  private sessionValidSubject = new BehaviorSubject<boolean>(false);
  public readonly sessionValid$ = this.sessionValidSubject.asObservable();

  constructor() {
    // Initialize session validation on service creation
    this.validateCurrentSession();
    
    // Set up periodic session validation if auto-refresh is enabled
    if (this.defaultConfig.autoRefresh) {
      this.setupPeriodicValidation();
    }
  }

  /**
   * Store user session data in sessionStorage
   */
  storeSession(loginResponse: LoginResponse, config?: Partial<SessionConfig>): void {
    const sessionConfig = { ...this.defaultConfig, ...config };
    const now = new Date();
    const expiresAt = new Date(now.getTime() + sessionConfig.sessionTimeoutMinutes * 60000);

    const sessionData: SessionData = {
      iduser: loginResponse.id,
      email: loginResponse.email,
      userRole: loginResponse.type_utilisateur || loginResponse.role || 'user',
      type_utilisateur: loginResponse.type_utilisateur,
      nom: loginResponse.nom,
      prenom: loginResponse.prenom,
      statut: loginResponse.statut,
      color: loginResponse.color,
      loginTime: now.toISOString(),
      expiresAt: expiresAt.toISOString(),
      lastActivity: now.toISOString()
    };

    // Store each piece of data
    Object.entries(sessionData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        const storageKey = this.getStorageKey(key as keyof SessionData);
        sessionStorage.setItem(storageKey, value.toString());
      }
    });

    this.sessionValidSubject.next(true);
  }

  /**
   * Retrieve complete session data
   */
  getSession(): SessionData | null {
    try {
      const userId = sessionStorage.getItem(this.SESSION_KEYS.USER_ID);
      const email = sessionStorage.getItem(this.SESSION_KEYS.EMAIL);
      const userRole = sessionStorage.getItem(this.SESSION_KEYS.ROLE);

      if (!userId || !email || !userRole) {
        return null;
      }

      const sessionData: SessionData = {
        iduser: parseInt(userId),
        email,
        userRole,
        type_utilisateur: sessionStorage.getItem(this.SESSION_KEYS.TYPE_UTILISATEUR) || undefined,
        nom: sessionStorage.getItem(this.SESSION_KEYS.NOM) || undefined,
        prenom: sessionStorage.getItem(this.SESSION_KEYS.PRENOM) || undefined,
        statut: sessionStorage.getItem(this.SESSION_KEYS.STATUT) || undefined,
        color: sessionStorage.getItem(this.SESSION_KEYS.COLOR) || undefined,
        loginTime: sessionStorage.getItem(this.SESSION_KEYS.LOGIN_TIME) || new Date().toISOString(),
        expiresAt: sessionStorage.getItem(this.SESSION_KEYS.EXPIRES_AT) || undefined,
        lastActivity: sessionStorage.getItem(this.SESSION_KEYS.LAST_ACTIVITY) || undefined
      };

      return sessionData;
    } catch (error) {
      console.error('Error retrieving session data:', error);
      return null;
    }
  }

  /**
   * Update last activity timestamp
   */
  updateLastActivity(): void {
    const now = new Date().toISOString();
    sessionStorage.setItem(this.SESSION_KEYS.LAST_ACTIVITY, now);
  }

  /**
   * Validate current session
   */
  validateSession(): SessionValidationResult {
    const sessionData = this.getSession();

    if (!sessionData) {
      return {
        isValid: false,
        reason: 'MISSING_DATA',
        message: 'No session data found'
      };
    }

    const now = new Date();

    // Check if session has expired
    if (sessionData.expiresAt) {
      const expiresAt = new Date(sessionData.expiresAt);
      if (now > expiresAt) {
        return {
          isValid: false,
          reason: 'EXPIRED',
          message: 'Session has expired'
        };
      }
    }

    // Check for inactivity timeout
    if (sessionData.lastActivity) {
      const lastActivity = new Date(sessionData.lastActivity);
      const inactivityLimit = new Date(lastActivity.getTime() + this.defaultConfig.inactivityTimeoutMinutes * 60000);
      
      if (now > inactivityLimit) {
        return {
          isValid: false,
          reason: 'INACTIVE_TOO_LONG',
          message: 'Session expired due to inactivity'
        };
      }
    }

    return {
      isValid: true
    };
  }

  /**
   * Clear all session data
   */
  clearSession(): void {
    Object.values(this.SESSION_KEYS).forEach(key => {
      sessionStorage.removeItem(key);
    });
    
    this.sessionValidSubject.next(false);
  }

  /**
   * Check if session exists and is valid
   */
  hasValidSession(): boolean {
    const validation = this.validateSession();
    return validation.isValid;
  }

  /**
   * Get specific session value
   */
  getSessionValue(key: keyof SessionData): string | null {
    const storageKey = this.getStorageKey(key);
    return sessionStorage.getItem(storageKey);
  }

  /**
   * Set specific session value
   */
  setSessionValue(key: keyof SessionData, value: string): void {
    const storageKey = this.getStorageKey(key);
    sessionStorage.setItem(storageKey, value);
    
    // Update last activity when setting values
    if (key !== 'lastActivity') {
      this.updateLastActivity();
    }
  }

  /**
   * Extend session expiration time
   */
  extendSession(additionalMinutes: number = 60): void {
    const currentExpiry = sessionStorage.getItem(this.SESSION_KEYS.EXPIRES_AT);
    if (currentExpiry) {
      const newExpiry = new Date(new Date(currentExpiry).getTime() + additionalMinutes * 60000);
      sessionStorage.setItem(this.SESSION_KEYS.EXPIRES_AT, newExpiry.toISOString());
    }
  }

  /**
   * Get session storage key mapping
   */
  private getStorageKey(key: keyof SessionData): string {
    const keyMap: Record<keyof SessionData, string> = {
      iduser: this.SESSION_KEYS.USER_ID,
      email: this.SESSION_KEYS.EMAIL,
      userRole: this.SESSION_KEYS.ROLE,
      type_utilisateur: this.SESSION_KEYS.TYPE_UTILISATEUR,
      nom: this.SESSION_KEYS.NOM,
      prenom: this.SESSION_KEYS.PRENOM,
      statut: this.SESSION_KEYS.STATUT,
      color: this.SESSION_KEYS.COLOR,
      loginTime: this.SESSION_KEYS.LOGIN_TIME,
      expiresAt: this.SESSION_KEYS.EXPIRES_AT,
      lastActivity: this.SESSION_KEYS.LAST_ACTIVITY
    };
    
    return keyMap[key];
  }

  /**
   * Validate current session and update subject
   */
  private validateCurrentSession(): void {
    const validation = this.validateSession();
    this.sessionValidSubject.next(validation.isValid);
  }

  /**
   * Set up periodic session validation
   */
  private setupPeriodicValidation(): void {
    // Check session validity every 5 minutes
    setInterval(() => {
      this.validateCurrentSession();
    }, 5 * 60 * 1000);
  }

  /**
   * Update the color preference in session storage
   * @param color - The new color preference
   */
  updateSessionColor(color: string): void {
    try {
      sessionStorage.setItem(this.SESSION_KEYS.COLOR, color);
      // Update the session data in memory if needed
      const currentSession = this.getSession();
      if (currentSession) {
        currentSession.color = color;
      }
    } catch (error) {
      console.error('Error updating session color:', error);
    }
  }

  /**
   * Get the current color preference from session
   * @returns The current color preference or null if not set
   */
  getSessionColor(): string | null {
    try {
      return sessionStorage.getItem(this.SESSION_KEYS.COLOR);
    } catch (error) {
      console.error('Error retrieving session color:', error);
      return null;
    }
  }
}
