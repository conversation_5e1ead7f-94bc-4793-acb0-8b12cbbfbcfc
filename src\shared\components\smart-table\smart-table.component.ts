import { Component, Input, Output, EventEmitter, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { TableColumn, TableAction, TableConfig, StatusConfig, ActionButton } from '../../models/table.models';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FrenchDatePipe } from '../../pipes/french-date.pipe';
import { IconModule } from '@coreui/icons-angular';

@Component({
  standalone: true,
  selector: 'app-smart-table',
  imports: [CommonModule, FormsModule, FrenchDatePipe, IconModule],
  templateUrl: './smart-table.component.html',
  styleUrls: ['./smart-table.component.scss']
})
export class SmartTableComponent {
  @Input() columns: TableColumn[] = [];
  @Input() set data(data: any[]) {
    this._data = data || [];
    this.applyFilter();
  }
  @Input() actions: TableAction[] = [];
  @Input() actionButtons: ActionButton[] = [];
  @Input() showActionsColumn: boolean = true;
  @Input() config: TableConfig = {
    pageSizeOptions: [5, 10, 25],
    pageSize: 10,
    selectable: false,
    multiSelect: false,
    emptyMessage: 'No data available',
    striped: true,
    hover: true,
    bordered: false,
    small: false,
    showFilter: true
  };
  @Input() isLoading: boolean = false;
  
  @Output() actionClick = new EventEmitter<{action: string, row: any}>();
  @Output() selectionChange = new EventEmitter<any[]>();

  @ViewChild('filterInput') filterInput!: ElementRef;
  
  _data: any[] = [];
  filteredData: any[] = [];
  paginatedData: any[] = [];
  selectedRows = new Set<any>();
  currentPage = 1;
  pageSize = 10;
  totalPages = 1;
  sortColumn = '';
  sortDirection: 'asc' | 'desc' = 'asc';
  filterText = '';

  get displayedColumns(): string[] {
    let cols = this.columns.map(col => col.name);
    if (this.config.selectable) {
      cols = ['select', ...cols];
    }
    if (this.showActionsColumn && (this.visibleActions.length > 0 || this.visibleActionButtons.length > 0)) {
      return [...cols, 'actions'];
    }
    return cols;
  }

  get visibleActions(): TableAction[] {
    return this.actions.filter(action => !action.hidden);
  }

  get visibleActionButtons(): ActionButton[] {
    return this.actionButtons || [];
  }

  get hasAnyActions(): boolean {
    return this.visibleActions.length > 0 || this.visibleActionButtons.length > 0;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['config']) {
      this.pageSize = this.config.pageSize || 10;
    }
    if (changes['data'] || changes['config']) {
      this.applyFilter();
    }
  }

  applyFilter() {
    if (!this.filterText) {
      this.filteredData = [...this._data];
    } else {
      const filterValue = this.filterText.toLowerCase();
      this.filteredData = this._data.filter(item => {
        return this.columns.some(column => {
          if (column.filterable === false) return false;

          let value = String(item[column.name]).toLowerCase();

          // For date columns, also check the French formatted date
          if (column.dataType === 'date') {
            const frenchDatePipe = new FrenchDatePipe();
            const frenchDate = frenchDatePipe.transform(item[column.name]).toLowerCase();
            return value.includes(filterValue) || frenchDate.includes(filterValue);
          }

          return value.includes(filterValue);
        });
      });
    }
    this.currentPage = 1;
    this.updatePaginatedData();
  }

  clearFilter() {
    this.filterText = '';
    this.applyFilter();
    this.filterInput.nativeElement.focus();
  }

  updatePaginatedData() {
    let sortedData = [...this.filteredData];
    if (this.sortColumn) {
      sortedData.sort((a, b) => {
        const valA = a[this.sortColumn];
        const valB = b[this.sortColumn];
        const col = this.columns.find(c => c.name === this.sortColumn);
        
        if (col?.dataType === 'date') {
          return this.sortDirection === 'asc' 
            ? new Date(valA).getTime() - new Date(valB).getTime()
            : new Date(valB).getTime() - new Date(valA).getTime();
        } else if (col?.dataType === 'number') {
          return this.sortDirection === 'asc' ? valA - valB : valB - valA;
        } else {
          return this.sortDirection === 'asc' 
            ? String(valA).localeCompare(String(valB))
            : String(valB).localeCompare(String(valA));
        }
      });
    }

    this.totalPages = Math.ceil(sortedData.length / this.pageSize);
    const startIndex = (this.currentPage - 1) * this.pageSize;
    this.paginatedData = sortedData.slice(startIndex, startIndex + this.pageSize);
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.updatePaginatedData();
  }

  onPageSizeChange(size: number) {
    this.pageSize = size;
    this.currentPage = 1;
    this.updatePaginatedData();
  }

  onSort(column: string) {
    const col = this.columns.find(c => c.name === column);
    if (!col?.sortable) return;

    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }

    this.updatePaginatedData();
  }

  getColumnClass(column: TableColumn): string {
    const classes: string[] = [];
    if (column.dataType) {
      classes.push(`${column.dataType}-column`);
    }
    if (column.name === 'active') {
      classes.push('active-column');
    }
    return classes.join(' ');
  }

  getStatusConfig(column: TableColumn, value: any): StatusConfig | null {
    if (column.dataType !== 'status' || !column.statusConfig) {
      return null;
    }
    return column.statusConfig.find(config => config.value === value) || null;
  }

  getStatusDisplay(column: TableColumn, value: any): { text: string; config: StatusConfig | null } {
    const config = this.getStatusConfig(column, value);
    return {
      text: config?.displayText || String(value),
      config: config
    };
  }

  getSortIcon(column: string): string {
    if (this.sortColumn !== column) return 'cilSwapVertical';
    return this.sortDirection === 'asc' ? 'cilSortAscending' : 'cilSortDescending';
  }

  getIconName(iconClass: string): string {
    // Convert old icon class names to CoreUI icon names
    const iconMap: { [key: string]: string } = {
      'cil-pencil': 'cilPencil',
      'cil-trash': 'cilTrash',
      'cil-info': 'cilInfo',
      'cil-eye': 'cilEye',
      'cil-check': 'cilCheck',
      'cil-x': 'cilX',
      'cil-plus': 'cilPlus',
      'cil-minus': 'cilMinus',
      'cil-settings': 'cilSettings',
      'cil-user': 'cilUser',
      'cil-envelope': 'cilEnvelopeClosed',
      'cil-phone': 'cilPhone',
      'cil-location-pin': 'cilLocationPin',
      'cil-calendar': 'cilCalendar',
      'cil-clock': 'cilClock',
      'cil-star': 'cilStar',
      'cil-heart': 'cilHeart',
      'cil-thumb-up': 'cilThumbUp',
      'cil-thumb-down': 'cilThumbDown',
      'cil-share': 'cilShare',
      'cil-download': 'cilCloudDownload',
      'cil-upload': 'cilCloudUpload',
      'cil-print': 'cilPrint',
      'cil-copy': 'cilCopy',
      'cil-cut': 'cilCut',
      'cil-paste': 'cilDescription'
    };

    return iconMap[iconClass] || iconClass;
  }

  getEndIndex(): number {
    return Math.min(this.currentPage * this.pageSize, this.filteredData.length);
  }



  shouldShowAction(action: TableAction, row: any): boolean {
    if (action.hidden) return false;
    if (action.condition) {
      return action.condition(row);
    }
    return true;
  }

  onActionClick(actionName: string, row: any): void {
    // Check if this is a legacy action with callback
    const action = this.actions.find(a => a.name === actionName);
    if (action && action.callback) {
      action.callback(row);
    } else {
      this.actionClick.emit({action: actionName, row});
    }
  }

  onActionButtonClick(actionButton: ActionButton, row: any): void {
    if (actionButton.callback) {
      actionButton.callback(row);
    }
  }

  shouldShowActionButton(actionButton: ActionButton, row: any): boolean {
    if (actionButton.condition && !actionButton.condition(row)) {
      return false;
    }
    return true;
  }

  isActionButtonDisabled(actionButton: ActionButton, row: any): boolean {
    if (actionButton.disabled) {
      return actionButton.disabled(row);
    }
    return false;
  }

  toggleRowSelection(row: any) {
    if (this.config.multiSelect) {
      if (this.selectedRows.has(row)) {
        this.selectedRows.delete(row);
      } else {
        this.selectedRows.add(row);
      }
    } else {
      this.selectedRows.clear();
      this.selectedRows.add(row);
    }
    this.selectionChange.emit(Array.from(this.selectedRows));
  }

  isSelected(row: any): boolean {
    return this.selectedRows.has(row);
  }

  masterToggle() {
    if (this.isAllSelected()) {
      this.selectedRows.clear();
    } else {
      this.paginatedData.forEach(row => this.selectedRows.add(row));
    }
    this.selectionChange.emit(Array.from(this.selectedRows));
  }

  isAllSelected(): boolean {
    return this.paginatedData.length > 0 && 
           this.selectedRows.size === this.paginatedData.length;
  }

  getPages(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    if (this.totalPages <= maxVisiblePages) {
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = startPage + maxVisiblePages - 1;
      
      if (endPage > this.totalPages) {
        endPage = this.totalPages;
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
      
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push(-1);
        }
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      if (endPage < this.totalPages) {
        if (endPage < this.totalPages - 1) {
          pages.push(-1);
        }
        pages.push(this.totalPages);
      }
    }
    
    return pages;
  }
}