<div class="bg-light dark:bg-transparent min-vh-100 d-flex flex-row align-items-center">
  <c-container breakpoint="md">
    <c-row class="justify-content-center">
      <c-col lg="10" xl="8">
        <c-card-group>
          <c-card class="p-4">
            <c-card-body>
              <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" cForm>
                <h1>Login</h1>
                <p class="text-body-secondary">Sign In to your account</p>

                <!-- Error Message Display -->
                <div *ngIf="errorMessage" class="alert alert-danger mb-3" role="alert">
                  {{ errorMessage }}
                </div>

                <!-- Email Input -->
                <c-input-group class="mb-3">
                  <span cInputGroupText>
                    <svg cIcon name="cilUser"></svg>
                  </span>
                  <input
                    autoComplete="email"
                    cFormControl
                    placeholder="Email"
                    type="email"
                    formControlName="email"
                    [class.is-invalid]="isFieldInvalid('email')"
                  />
                </c-input-group>

                <!-- Email Validation Error -->
                <div *ngIf="isFieldInvalid('email')" class="invalid-feedback d-block mb-2">
                  {{ getFieldError('email') }}
                </div>

                <!-- Password Input -->
                <c-input-group class="mb-4">
                  <span cInputGroupText>
                    <svg cIcon name="cilLockLocked"></svg>
                  </span>
                  <input
                    autoComplete="current-password"
                    cFormControl
                    placeholder="Password"
                    type="password"
                    formControlName="password"
                    [class.is-invalid]="isFieldInvalid('password')"
                  />
                </c-input-group>

                <!-- Password Validation Error -->
                <div *ngIf="isFieldInvalid('password')" class="invalid-feedback d-block mb-2">
                  {{ getFieldError('password') }}
                </div>

                <c-row>
                  <c-col xs="6">
                    <button
                      type="submit"
                      cButton
                      class="px-4"
                      color="primary"
                      [disabled]="isLoading || loginForm.invalid"
                    >
                      <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      {{ isLoading ? 'Signing In...' : 'Login' }}
                    </button>
                  </c-col>
                  <c-col class="text-end" xs="6">
                    <button cButton class="px-0" color="link" type="button">
                      Forgot password?
                    </button>
                  </c-col>
                </c-row>
              </form>
            </c-card-body>
          </c-card>
          <c-card [ngStyle]="{'minWidth.%': 44}" class="text-white bg-primary py-5">
            <c-card-body class="text-center">
              <div>
                <h2>Sign up</h2>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
                  tempor incididunt ut labore et dolore magna aliqua.
                </p>
                <button [active]="true" cButton class="mt-3" color="primary" routerLink="/register">
                  Register Now!
                </button>
              </div>
            </c-card-body>
          </c-card>
        </c-card-group>
      </c-col>
    </c-row>
  </c-container>
</div>
