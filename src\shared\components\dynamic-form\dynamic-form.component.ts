import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormField, FormAction, FormConfig } from '../../models/form.models';
import { FormBuilder, FormGroup, FormArray, FormControl, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IconModule } from '@coreui/icons-angular';

@Component({
  standalone: true,
  selector: 'app-dynamic-form',
  templateUrl: './dynamic-form.component.html',
  styleUrls: ['./dynamic-form.component.scss'],
  imports: [CommonModule,ReactiveFormsModule]
})
export class DynamicFormComponent {
  @Input() config: FormConfig = { fields: [], actions: [] };
  @Output() formSubmit = new EventEmitter<any>();
  
  form: FormGroup;
  imagePreviews: { [key: string]: string | ArrayBuffer | null } = {};

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({});
  }

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    const group: any = {};
    
    this.config.fields.forEach(field => {
      const validators : any[] = [];
      if (field.required) {
        validators.push(Validators.required);
      }
      
      if (field.type === 'multiselect') {
        group[field.name] = this.fb.array(
          field.defaultValue?.map((val: any) => new FormControl(val)) || []
        );
      } else {
        group[field.name] = [field.defaultValue || '', validators];
      }
    });
    
    this.form = this.fb.group(group);
  }

  onFileChange(event: any, fieldName: string) {
    const files = event.target.files;
    if (files && files.length > 0) {
      const field = this.config.fields.find(f => f.name === fieldName);
      
      if (field?.multipleImage) {
        // Handle multiple files
        const fileArray = Array.from(files);
        this.form.get(fieldName)?.setValue(fileArray);
        
        // Preview first image if it's an image
        if (files[0].type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = () => {
            this.imagePreviews[fieldName] = reader.result;
          };
          reader.readAsDataURL(files[0]);
        }
      } else {
        // Handle single file
        const file = files[0];
        this.form.get(fieldName)?.setValue(file);
        
        // Create preview if it's an image
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = () => {
            this.imagePreviews[fieldName] = reader.result;
          };
          reader.readAsDataURL(file);
        }
      }
    }
  }

  removeImage(fieldName: string) {
    this.form.get(fieldName)?.setValue(null);
    delete this.imagePreviews[fieldName];
  }

  getSelectOptions(fieldName: string) {
    const field = this.config.fields.find(f => f.name === fieldName);
    return field?.options || [];
  }

  getOptionLabel(fieldName: string, option: any): string {
    const field = this.config.fields.find(f => f.name === fieldName);
    return field?.optionLabel ? option[field.optionLabel] : String(option);
  }

  getOptionValue(fieldName: string, option: any): any {
    const field = this.config.fields.find(f => f.name === fieldName);
    return field?.optionValue ? option[field.optionValue] : option;
  }

  onSelectChange(fieldName: string, value: string) {
    const field = this.config.fields.find(f => f.name === fieldName);
    if (!field || !field.options) return;

    // Find the full option object
    const selectedOption = field.options.find(opt => 
      this.getOptionValue(fieldName, opt) === value
    );
    
    this.form.get(fieldName)?.setValue(selectedOption || null);
  }

  onMultiSelectChange(fieldName: string, value: any, isChecked: boolean) {
    const formArray = this.form.get(fieldName) as FormArray;
    const field = this.config.fields.find(f => f.name === fieldName);
    
    if (!field || !field.options) return;

    // Find the full option object
    const selectedOption = field.options.find(opt => 
      this.getOptionValue(fieldName, opt) === value
    );

    if (isChecked && selectedOption) {
      formArray.push(new FormControl(selectedOption));
    } else {
      const index = formArray.controls.findIndex(ctrl => 
        this.getOptionValue(fieldName, ctrl.value) === value
      );
      if (index >= 0) {
        formArray.removeAt(index);
      }
    }
  }

  isSelected(fieldName: string, value: any): boolean {
  const formArray = this.form.get(fieldName);
  if (!(formArray instanceof FormArray)) return false;
  return formArray.controls.some(ctrl =>
    this.getOptionValue(fieldName, ctrl.value) === value
  );
}

  onSubmit() {
    if (this.form.valid) {
      const formData = new FormData();
      const formValue = this.form.value;
      
      // Handle file inputs and prepare FormData
      Object.keys(formValue).forEach(key => {
        const field = this.config.fields.find(f => f.name === key);
        
        if (field?.type === 'image') {
          if (field.multipleImage) {
            // Handle multiple files
            (formValue[key] || []).forEach((file: File) => {
              formData.append(key, file);
            });
          } else if (formValue[key]) {
            // Handle single file
            formData.append(key, formValue[key]);
          }
        } else if (field?.type === 'multiselect') {
          // For multiselect, we might want to send just the values
          const values = formValue[key].map((item: any) => 
            field.optionValue ? item[field.optionValue] : item
          );
          formData.append(key, JSON.stringify(values));
        } else if (field?.type === 'select') {
          // For select, we might want to send just the value
          const value = field.optionValue ? formValue[key]?.[field.optionValue] : formValue[key];
          if (value !== undefined && value !== null) {
            formData.append(key, value);
          }
        } else {
          // Regular fields
          if (formValue[key] !== undefined && formValue[key] !== null) {
            formData.append(key, formValue[key]);
          }
        }
      });
      
      this.formSubmit.emit(formData);
    } else {
      this.markFormGroupTouched(this.form);
    }
  }

  onActionClick(action: FormAction) {
    if (action.type === 'submit') {
      this.onSubmit();
    } else {
      action.onClick(this.form.value);
    }
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          arrayControl.markAsTouched();
        });
      }
    });
  }
}