import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { AuthService } from '../services/auth.service';
import { LoginRequest } from '../models/auth.interfaces';

@Component({
  selector: 'app-test-http',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div>
      <h3>HTTP Test Component</h3>
      <button (click)="testHttpRequest()">Test Basic HTTP Request</button>
      <button (click)="testLogin()">Test Login with Provided Credentials</button>
      <div *ngIf="result">
        <h4>Result:</h4>
        <pre>{{ result | json }}</pre>
      </div>
      <div *ngIf="error" style="color: red;">
        <h4>Error:</h4>
        <pre>{{ error }}</pre>
      </div>
      <div *ngIf="authService.loading$ | async">
        <p>Loading...</p>
      </div>
    </div>
  `
})
export class TestHttpComponent implements OnInit {
  result: any = null;
  error: string = '';

  constructor(private http: HttpClient, public authService: AuthService) {}

  ngOnInit() {
    console.log('TestHttpComponent initialized - HttpClient injected successfully!');
  }

  testHttpRequest() {
    const httpOptions = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + btoa('med:123456')
      })
    };

    // Test a simple GET request to the backend API
    this.http.get('http://localhost:3000/api/customers', httpOptions).subscribe({
      next: (response) => {
        this.result = response;
        this.error = '';
        console.log('HTTP request successful:', response);
      },
      error: (err) => {
        this.error = `HTTP Error: ${err.status} - ${err.message}`;
        this.result = null;
        console.error('HTTP request failed:', err);
      }
    });
  }

  testLogin() {
    const loginData: LoginRequest = {
      email: '<EMAIL>',
      mot_de_passe: '89waAmc4XA9'
    };

    this.authService.login(loginData).subscribe({
      next: (response) => {
        this.result = response;
        this.error = '';
        console.log('Login successful:', response);
      },
      error: (err) => {
        this.error = `Login Error: ${err.type} - ${err.message}`;
        this.result = null;
        console.error('Login failed:', err);
      }
    });
  }
}
