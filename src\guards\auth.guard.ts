import { inject } from '@angular/core';
import { CanActivateFn, CanActivateChildFn, Router } from '@angular/router';
import { map, take, catchError } from 'rxjs';
import { of } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { SessionStorageService } from '../services/session-storage.service';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const sessionStorageService = inject(SessionStorageService);
  const router = inject(Router);

  // First check if there's a valid session
  const sessionValidation = sessionStorageService.validateSession();
  if (!sessionValidation.isValid) {
    console.log('Session invalid:', sessionValidation.reason, sessionValidation.message);

    // Clear invalid session
    sessionStorageService.clearSession();

    // Store the attempted URL for redirecting after login
    sessionStorage.setItem('redirectUrl', state.url);

    // Navigate to login page
    router.navigate(['/login']);
    return false;
  }

  return authService.isAuthenticated$.pipe(
    take(1),
    map(isAuthenticated => {
      if (isAuthenticated) {
        // Update last activity on successful access
        sessionStorageService.updateLastActivity();
        return true;
      } else {
        // User is not authenticated, redirect to login
        console.log('Access denied. User not authenticated. Redirecting to login...');

        // Store the attempted URL for redirecting after login
        sessionStorage.setItem('redirectUrl', state.url);

        // Navigate to login page
        router.navigate(['/login']);
        return false;
      }
    }),
    catchError(error => {
      console.error('Auth guard error:', error);

      // On error, redirect to login
      sessionStorage.setItem('redirectUrl', state.url);
      router.navigate(['/login']);
      return of(false);
    })
  );
};

export const authChildGuard: CanActivateChildFn = (childRoute, state) => {
  return authGuard(childRoute, state);
};
