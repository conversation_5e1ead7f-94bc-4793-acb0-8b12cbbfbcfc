import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class LocationService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Get all cities
   * @returns Observable with cities list
   * @originalName getVilles
   */
  getAllCities(): Observable<any[]> {
    return this.http.get<any[]>(this.apiURL + 'ville', httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find region by city ID
   * @param cityId - City ID
   * @returns Observable with region data
   * @originalName findRegion
   */
  findRegionByCityId(cityId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'region/' + cityId, httpOptions);
  }

  /**
   * Add loading point
   * @param loadingPointData - Loading point data
   * @returns Observable with creation result
   */
  addLoadingPoint(loadingPointData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'ptchargement', loadingPointData, httpOptions);
  }

  /**
   * Add charging point
   * @param chargingData - Charging point data
   * @returns Observable with creation result
   */
  addChargingPoint(chargingData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'ptchargement', chargingData, httpOptions);
  }

  /**
   * Add charging/discharging points by order ID
   * @param orderId - Order ID
   * @returns Observable with points data
   * @originalName addCharDecharByIdCmd
   */
  addChargingDischargingByOrderId(orderId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'ptchargDecharg/' + orderId, httpOptions);
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
