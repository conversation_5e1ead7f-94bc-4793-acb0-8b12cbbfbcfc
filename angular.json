{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"coreui-free-angular-admin-template": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist/coreui-free-angular-admin-template", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["@angular/localize/init", "zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "preserveSymlinks": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/scss/styles.scss"], "scripts": [], "allowedCommonJsDependencies": [], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": []}}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "7mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "coreui-free-angular-admin-template:build:production"}, "development": {"buildTarget": "coreui-free-angular-admin-template:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "coreui-free-angular-admin-template:build"}}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/scss/styles.scss"], "scripts": []}}}}}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}