import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class OrderLineService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Add new order line
   * @param lineData - Line data
   * @returns Observable with creation result
   * @originalName addLigne
   */
  addLine(lineData: any): Observable<any> {
    const endpoint = `${this.apiURL}ptchargement`;
    return this.http.post<any>(endpoint, lineData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update line volume
   * @param id - Line ID
   * @param newVolume - New volume value
   * @returns Observable with update result
   * @originalName updateVolume
   */
  updateVolume(id: number, newVolume: number): Observable<any> {
    const url = `${this.apiURL}ptchargement/${id}`;
    const body = { volume: newVolume };
    return this.http.put(url, body, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update status to valid
   * @param id - Line ID
   * @returns Observable with update result
   * @originalName updateStatusValid
   */
  updateStatusValid(id: number): Observable<any> {
    const url = `${this.apiURL}ptchargement/valid/${id}`;
    return this.http.put(url, null, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update status to no pickup
   * @param id - Line ID
   * @returns Observable with update result
   * @originalName updateStatusNoPickup
   */
  updateStatusNoPickup(id: number): Observable<any> {
    const url = `${this.apiURL}ptchargement/updateStatusNoPickup/${id}`;
    return this.http.put(url, null, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update status to reserved
   * @param data - Update data with array of updates
   * @returns Observable with update result
   * @originalName updateStatusReserved
   */
  updateStatusReserved(data: { updates: any[] }): Observable<any> {
    const url = `${this.apiURL}ptchargement/reserved`;
    return this.http.post(url, data, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find all valid lines
   * @returns Observable with valid lines list
   * @originalName findAllValid
   */
  findAllValid(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'ptchargement/valid/', httpOptions);
  }

  /**
   * Find reserved lines by date and truck
   * @param searchData - Search criteria
   * @returns Observable with reserved lines
   * @originalName findReservedByDateAndCamion
   */
  findReservedByDateAndTruck(searchData: any): Observable<any> {
    let params = new HttpParams();
    
    if (searchData.id_camion) {
      params = params.set('id_camion', searchData.id_camion);
    }
    if (searchData.date) {
      params = params.set('date', searchData.date);
    }
    if (searchData.id_conducteur) {
      params = params.set('id_conducteur', searchData.id_conducteur);
    }

    const url = this.apiURL + 'pt/reserved';
    return this.http.get<any>(url, { headers: httpOptions.headers, params });
  }

  /**
   * Find expedited lines by date and truck
   * @param searchData - Search criteria
   * @returns Observable with expedited lines
   * @originalName findExpediedByDateAndCamion
   */
  findExpeditedByDateAndTruck(searchData: any): Observable<any> {
    let params = new HttpParams();
    
    if (searchData.id_camion) {
      params = params.set('id_camion', searchData.id_camion);
    }
    if (searchData.date) {
      params = params.set('date', searchData.date);
    }
    if (searchData.id_conducteur) {
      params = params.set('id_conducteur', searchData.id_conducteur);
    }

    const url = this.apiURL + 'pt/expedied';
    return this.http.get<any>(url, { headers: httpOptions.headers, params });
  }

  /**
   * Find lines to adjust
   * @param searchData - Search criteria
   * @returns Observable with lines to adjust
   * @originalName findLineToAdjust
   */
  findLinesToAdjust(searchData: any): Observable<any> {
    let params = new HttpParams();
    
    if (searchData.id_camion) {
      params = params.set('id_camion', searchData.id_camion);
    }
    if (searchData.date) {
      params = params.set('date', searchData.date);
    }
    if (searchData.id_conducteur) {
      params = params.set('id_conducteur', searchData.id_conducteur);
    }

    const url = this.apiURL + 'pt/findLineToAdjust';
    return this.http.get<any>(url, { headers: httpOptions.headers, params });
  }

  /**
   * Update reservation status
   * @param id - Line ID
   * @param data - Update data
   * @returns Observable with update result
   * @originalName updateStatusReseration
   */
  updateReservationStatus(id: any, data: any): Observable<any> {
    const url = `${this.apiURL}ptchargement/reservation/${id}`;
    return this.http.put(url, data, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update expedition status
   * @param data - Update data
   * @returns Observable with update result
   * @originalName updateStatusExpedition
   */
  updateExpeditionStatus(data: any): Observable<any> {
    const url = `${this.apiURL}ptchargement/expedition/${data.id}`;
    return this.http.put(url, data, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update delivery status
   * @param id - Line ID
   * @returns Observable with update result
   * @originalName updateStatusLivred
   */
  updateDeliveryStatus(id: any): Observable<any> {
    const url = `${this.apiURL}ptchargement/livred/${id}`;
    return this.http.put(url, null, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find delivered lines by date and client
   * @param searchData - Search criteria
   * @returns Observable with delivered lines
   * @originalName findLivredByDateAndClient
   */
  findDeliveredByDateAndClient(searchData: any): Observable<any> {
    let params = new HttpParams();
    
    if (searchData.date_debut) {
      params = params.set('date_debut', searchData.date_debut);
    }
    if (searchData.date_fin) {
      params = params.set('date_fin', searchData.date_fin);
    }
    if (searchData.id_client) {
      params = params.set('id_client', searchData.id_client);
    }

    const url = this.apiURL + 'ligneCmd/toFac';
    return this.http.get<any>(url, { headers: httpOptions.headers, params });
  }

  /**
   * Find delivered lines by date and type
   * @param searchData - Search criteria
   * @returns Observable with delivered lines
   * @originalName findLivredByDateAndType
   */
  findDeliveredByDateAndType(searchData: any): Observable<any> {
    let params = new HttpParams();
    
    if (searchData.date_debut) {
      params = params.set('date_debut', searchData.date_debut);
    }
    if (searchData.date_fin) {
      params = params.set('date_fin', searchData.date_fin);
    }
    if (searchData.type_ligne) {
      params = params.set('type_ligne', searchData.type_ligne);
    }

    const url = this.apiURL + 'ligneCmd/type';
    return this.http.get<any>(url, { headers: httpOptions.headers, params });
  }

  /**
   * Update client for line
   * @param data - Update data
   * @returns Observable with update result
   * @originalName updateClient
   */
  updateClient(data: any): Observable<any> {
    const url = `${this.apiURL}ptchargement/client/${data.id}`;
    return this.http.put(url, data, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find reserved lines by user ID
   * @param id - User ID
   * @returns Observable with reserved lines
   * @originalName findReservedByIdUser
   */
  findReservedByUser(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `ligneCmdByDemand/${id}`, httpOptions);
  }

  /**
   * Find expedited lines by user ID
   * @param id - User ID
   * @returns Observable with expedited lines
   * @originalName findExpediedByIdUser
   */
  findExpeditedByUser(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `ligneCmdExpByDemand/${id}`, httpOptions);
  }

  /**
   * Find delivered lines by user ID
   * @param id - User ID
   * @returns Observable with delivered lines
   * @originalName ligneCmdLivredByDemand
   */
  findDeliveredByUser(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `ligneCmdLivredByDemand/${id}`, httpOptions);
  }

  /**
   * Count valid lines
   * @returns Observable with count
   * @originalName countValid
   */
  countValid(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'countValid', httpOptions);
  }

  /**
   * Count reserved lines
   * @returns Observable with count
   * @originalName countReserved
   */
  countReserved(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'countReserved', httpOptions);
  }

  /**
   * Count expedited lines
   * @returns Observable with count
   * @originalName countExpedied
   */
  countExpedited(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'countExpedied', httpOptions);
  }

  /**
   * Count delivered lines
   * @returns Observable with count
   * @originalName countDelivred
   */
  countDelivered(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'countDelivred', httpOptions);
  }

  /**
   * Find voyage list for reserved
   * @returns Observable with voyage list
   * @originalName findVoyageListRes
   */
  findVoyageListReserved(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findVoyageList', httpOptions);
  }

  /**
   * Find voyage list for expedited
   * @returns Observable with voyage list
   * @originalName findVoyageListExp
   */
  findVoyageListExpedited(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findVoyageListExp', httpOptions);
  }

  /**
   * Find voyage list to adjust
   * @returns Observable with voyage list
   * @originalName findVoyageListToAdjust
   */
  findVoyageListToAdjust(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findVoyageListToAdjust', httpOptions);
  }

  /**
   * Find lines for inspection
   * @returns Observable with inspection lines
   * @originalName findToInspection
   */
  findToInspection(): Observable<any> {
    return this.http.get<any>(`${this.apiURL}findInspection`, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find all lines for inspection
   * @returns Observable with all inspection lines
   * @originalName findAllToInspection
   */
  findAllToInspection(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findAllToInspection', httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update piece CEGID
   * @param id - Line ID
   * @param numPiece - Piece number
   * @returns Observable with update result
   * @originalName updatePieceCegid
   */
  updatePieceCegid(id: number, numPiece: number): Observable<any> {
    const url = `${this.apiURL}updatePieceCegid/${id}`;
    const body = { comment: numPiece };

    return this.http.put(url, body, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find lines with PDF not updated
   * @returns Observable with lines list
   * @originalName FindLignepdfNotUpdated
   */
  findLinesPdfNotUpdated(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'FindLignepdfNotUpdated', httpOptions);
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
