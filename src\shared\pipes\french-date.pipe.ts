import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'frenchDate',
  standalone: true
})
export class FrenchDatePipe implements PipeTransform {
  
  private readonly frenchMonths = [
    'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
  ];

  transform(value: any): string {
    if (!value) {
      return '';
    }

    try {
      // Handle different input formats
      let date: Date;
      
      if (value instanceof Date) {
        date = value;
      } else if (typeof value === 'string') {
        // Handle ISO date strings (2023-02-20) or other formats
        date = new Date(value);
      } else {
        return '';
      }

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return '';
      }

      const day = date.getDate();
      const month = this.frenchMonths[date.getMonth()];
      const year = date.getFullYear();

      return `${day} ${month} ${year}`;
    } catch (error) {
      console.warn('FrenchDatePipe: Error parsing date', value, error);
      return '';
    }
  }
}
